<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="40dp"
    android:height="40dp"
    android:viewportWidth="40"
    android:viewportHeight="40">
  <path
      android:pathData="M20,0L20,0A20,20 0,0 1,40 20L40,20A20,20 0,0 1,20 40L20,40A20,20 0,0 1,0 20L0,20A20,20 0,0 1,20 0z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="-0.36442"
          android:startX="20"
          android:endY="41.7056"
          android:endX="20"
          android:type="linear">
        <item android:offset="0" android:color="#FF00B2FF"/>
        <item android:offset="1" android:color="#FF006AFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M23.572,12.8574H21.4291C20.4819,12.8574 19.5735,13.2337 18.9037,13.9035C18.234,14.5732 17.8577,15.4816 17.8577,16.4289V18.5717H15.7148V21.4288H17.8577V27.1431H20.7148V21.4288H22.8577L23.572,18.5717H20.7148V16.4289C20.7148,16.2394 20.7901,16.0577 20.9241,15.9238C21.058,15.7898 21.2397,15.7146 21.4291,15.7146H23.572V12.8574Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#ffffff"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
</vector>
