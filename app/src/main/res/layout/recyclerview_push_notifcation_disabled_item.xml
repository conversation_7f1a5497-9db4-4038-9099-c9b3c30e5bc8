<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:paddingVertical="16dp"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android">

        <com.google.android.material.switchmaterial.SwitchMaterial
            android:id="@+id/switchWidget"
            style="@style/text_style_100"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="false"
            android:enabled="false"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:textColor="@color/title_primary"
            android:theme="@style/SwitchTheme"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
             />

        <TextView
            android:id="@+id/switch_text"
            style="@style/text_style_100"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:fontFamily="@font/tt_norms_pro_medium"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintBottom_toTopOf="@id/switch_summary"
            android:textColor="@color/disable_primary"
            app:layout_constraintEnd_toStartOf="@+id/switchWidget"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/switchWidget"
            tools:text="Profile Visits" />

        <TextView
            android:id="@+id/switch_summary"
            style="@style/text_style_75"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:textColor="@color/disable_primary"
            app:layout_constraintBottom_toBottomOf="@+id/switchWidget"
            app:layout_constraintEnd_toStartOf="@+id/switchWidget"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/switch_text"
            tools:text="In order to use this notification, verify your profile." />
    </androidx.constraintlayout.widget.ConstraintLayout>

