<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingHorizontal="32dp"
    tools:context=".login.fragments.forgotpass.ForgotPassSetPhoneFragment">


    <TextView
        android:id="@+id/textView66"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="@string/phone"
        android:textColor="@color/title_primary"
        style="@style/text_style_100"
        android:fontFamily="@font/tt_norms_pro_normal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/phone_input_container"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginTop="4dp"
        android:background="@drawable/edit_text_rounded_corners_12_dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/textView66"
        app:layout_constraintTop_toBottomOf="@id/textView66">

        <com.rilixtech.widget.countrycodepicker.CountryCodePicker
            android:id="@+id/countrycodepicker"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            app:ccp_defaultNameCode="@string/default_country_code"
            app:ccp_enablePhoneAutoFormatter="true"
            app:ccp_hideNameCode="true"
            app:ccp_showFlag="false"
            app:ccp_textColor="@color/gray_200"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="parent" />

        <EditText
            android:id="@+id/phone_number_input"
            style="@style/text_style_100"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:background="@color/background"
            android:ems="10"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:gravity="start"
            android:hint="@string/enter_phone_number"
            android:imeOptions="actionDone"
            android:importantForAutofill="no"
            android:inputType="phone"
            android:textColor="@color/title_primary"
            android:textColorHint="@color/gray_200"
            android:textCursorDrawable="@drawable/cursor"
            app:layout_constraintBottom_toBottomOf="@+id/phone_input_separator"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/phone_input_separator"
            app:layout_constraintTop_toTopOf="@+id/phone_input_separator">
        </EditText>

        <View
            android:id="@+id/phone_input_separator"
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@color/border"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/phone_number_input"
            app:layout_constraintStart_toEndOf="@id/countrycodepicker"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/phone_error_text"
        style="@style/text_style_100"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:textColor="@color/red_500"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/phone_input_container"
        app:layout_constraintStart_toStartOf="@+id/phone_input_container"
        app:layout_constraintTop_toBottomOf="@id/phone_input_container"
        tools:text="@string/phone_number_is_not_valid"
        tools:visibility="visible" />


    <com.duaag.android.views.DuaButton
        android:id="@+id/btn_continue"
        android:layout_width="0dp"
        android:layout_height="52dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="32dp"
        android:clickable="false"
        android:enabled="false"
        android:stateListAnimator="@null"
        android:text="@string/send_request_forgot_password"
        app:buttonType="PrimaryWithState"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/phone_input_container"
        app:layout_constraintStart_toStartOf="@+id/phone_input_container"
        app:layout_constraintTop_toBottomOf="@+id/phone_error_text"
        app:layout_constraintVertical_bias="1.0" />
</androidx.constraintlayout.widget.ConstraintLayout>