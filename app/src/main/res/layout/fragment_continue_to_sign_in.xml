<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:gravity="center"
        android:layout_marginHorizontal="24dp"
        android:text="@string/you_are_already_part_of_dua"
        android:fontFamily="@font/tt_norms_pro_demibold"
        android:textColor="@color/title_primary"
        style="@style/text_style_300"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginHorizontal="24dp"
        android:gravity="center"
        android:text="@string/an_account_with_this_phone_number_exists_an"
        app:layout_constraintTop_toBottomOf="@id/title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:textColor="@color/description_primary"
        style="@style/text_style_200" />

    <Button
        android:id="@+id/continue_button"
        android:layout_width="0dp"
        android:layout_height="52dp"
        android:stateListAnimator="@null"
        android:layout_marginTop="32dp"
        android:layout_marginHorizontal="24dp"
        android:textAllCaps="false"
        android:text="@string/continue_text"
        android:fontFamily="@font/tt_norms_pro_medium"
        style="@style/text_style_200"
        android:background="@drawable/pink_button_32_rounded"
        app:layout_constraintTop_toBottomOf="@id/description"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <Button
        android:id="@+id/dismiss"
        android:layout_width="0dp"
        android:layout_height="52dp"
        android:stateListAnimator="@null"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="24dp"
        android:layout_marginHorizontal="24dp"
        android:textAllCaps="false"
        android:text="@string/not_now"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:textColor="@color/title_primary"
        style="@style/text_style_200"
        android:background="@drawable/modal_transparent_button_rounded_32dp"
        app:layout_constraintTop_toBottomOf="@id/continue_button"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>