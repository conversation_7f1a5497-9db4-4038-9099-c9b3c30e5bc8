<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context="com.duaag.android.settings.fragments.account_settings.delete_account.DeleteAccountPermanentlyFragment">


        <include
            android:id="@+id/ghost_mode_view"
            layout="@layout/incognito_mode_view_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView9" />
        <TextView
            android:id="@+id/textView9"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="22dp"
            android:layout_marginEnd="16dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:textColor="@color/description_primary"
            style="@style/text_style_100"
            android:text="@string/you_have_30_days_to_cancel_account_deletion"
            android:textAlignment="center"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/delete_account_header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_marginTop="42dp"
            android:fontFamily="@font/tt_norms_pro_bold"
            android:textColor="@color/title_primary"
            style="@style/text_style_400"
            android:text="@string/delete_account"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ghost_mode_view" />

        <TextView
            android:id="@+id/delete_acc_desc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="18dp"
            android:text="@string/after_30_days"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:textColor="@color/description_primary"
            style="@style/text_style_100"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/delete_account_header" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/card_delete_permanently"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/ripple_background_item"
            android:elevation="0dp"
            android:paddingTop="24dp"
            android:paddingBottom="24dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/delete_acc_desc">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_chevron_right"
                app:layout_constraintBottom_toBottomOf="@+id/delete_acc_permanently_title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/delete_acc_permanently_title" />

            <TextView
                android:id="@+id/delete_acc_permanently_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:textColor="@color/title_primary"
                style="@style/text_style_100"
                android:text="@string/delete_my_account_permanently"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/imageView23"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:background="@color/border"
            app:layout_constraintBottom_toTopOf="@+id/card_delete_permanently"
            app:layout_constraintEnd_toEndOf="@+id/card_delete_permanently"
            app:layout_constraintStart_toStartOf="@+id/card_delete_permanently" />

        <View
            android:id="@+id/imageView26"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:background="@color/border"
            app:layout_constraintEnd_toEndOf="@+id/card_delete_permanently"
            app:layout_constraintStart_toStartOf="@+id/card_delete_permanently"
            app:layout_constraintTop_toBottomOf="@+id/card_delete_permanently" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>