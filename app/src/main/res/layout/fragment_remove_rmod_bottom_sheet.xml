<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:animateLayoutChanges="true">

    <TextView
        android:id="@+id/compatibility_score_text"
        style="@style/text_style_400"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="32dp"
        android:layout_marginBottom="7dp"
        android:layout_marginTop="32dp"
        android:fontFamily="@font/tt_norms_pro_demibold"
        android:text="@string/remove_recommended_match"
        android:textAlignment="center"
        android:textColor="@color/title_primary"
        app:layout_constraintBottom_toTopOf="@id/description"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="32dp" />

    <TextView
        android:id="@+id/description"
        style="@style/text_style_200"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="32dp"
        android:layout_marginHorizontal="32dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:text="@string/remove_recommended_match_desc"
        android:textAlignment="textStart"
        android:textColor="@color/description_secondary"
        app:layout_constraintBottom_toTopOf="@+id/remove_container" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/remove_container"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:layout_marginBottom="42dp"
        android:background="@color/transparent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/description"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@+id/description">

        <ImageView
            android:id="@+id/close_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_close"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/title_primary" />

        <TextView
            style="@style/text_style_200"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:text="@string/remove_title"
            android:textAllCaps="false"
            android:textColor="@color/title_primary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/close_icon"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>