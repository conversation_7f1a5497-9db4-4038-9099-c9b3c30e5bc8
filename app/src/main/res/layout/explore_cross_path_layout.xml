<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:context=".crosspath.presentation.fragment.CrossPathMainFragment">

    <androidx.cardview.widget.CardView
        android:id="@+id/spotted_in_real_life_card"
        android:layout_width="0dp"
        android:layout_height="150dp"
        android:layout_marginEnd="8dp"
        android:layout_marginStart="24dp"
        android:clickable="true"
        android:focusable="true"
        android:foreground="?attr/selectableItemBackground"
        app:cardElevation="0dp"
        app:cardCornerRadius="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/people_nearby_card"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/bg_card"
            android:padding="16dp">

            <ImageView
                android:id="@+id/firstCardIcon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:layout_marginStart="10dp"
                app:tint="@color/title_primary"
                android:src="@drawable/spotted_32_icon"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/firstCardText"
                style="@style/text_style_200"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:text="@string/spotted_in_real_life_label"
                android:textAlignment="textStart"
                app:layout_constraintHorizontal_bias="0"
                android:textColor="@color/title_primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/people_nearby_card"
        android:layout_width="0dp"
        android:layout_height="150dp"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="24dp"
        android:clickable="true"
        android:focusable="true"
        android:foreground="?attr/selectableItemBackground"
        app:cardElevation="0dp"
        app:cardCornerRadius="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/spotted_in_real_life_card"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/bg_card"
            android:padding="16dp">

            <ImageView
                android:id="@+id/secondCardIcon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:layout_marginStart="10dp"
                app:tint="@color/title_primary"
                android:src="@drawable/map_pin_icon"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/secondCardComingSoon"
                style="@style/text_style_100"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="2dp"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:text="@string/comming_soon"
                android:textAlignment="viewStart"
                app:layout_constraintHorizontal_bias="0"
                android:textColor="@color/description_primary"
                app:layout_constraintBottom_toTopOf="@+id/secondCardText"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/secondCardIcon"
                app:layout_constraintVertical_bias="1" />

            <TextView
                android:id="@+id/secondCardText"
                style="@style/text_style_200"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:text="@string/people_nearby_label"
                android:textAlignment="textStart"
                app:layout_constraintHorizontal_bias="0"
                android:textColor="@color/title_primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>
