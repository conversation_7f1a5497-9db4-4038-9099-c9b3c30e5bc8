<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true">

    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:indeterminate="true"
        android:theme="@style/ProgressBarTheme"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/change_your_gender_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:layout_marginHorizontal="16dp"
        android:fontFamily="@font/tt_norms_pro_bold"
        android:textColor="@color/title_primary"
        style="@style/text_style_400"
        android:text="@string/change_your_gender"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/change_gender_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:textColor="@color/description_primary"
        style="@style/text_style_100"
        app:layout_constraintEnd_toEndOf="@+id/change_your_gender_title"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@+id/change_your_gender_title"
        app:layout_constraintTop_toBottomOf="@+id/change_your_gender_title"
        tools:text="@string/your_gender_will_appear_in_your_profile" />

    <TextView
        android:id="@+id/textView42"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:layout_marginHorizontal="16dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:textColor="@color/title_primary"
        style="@style/text_style_100"
        android:text="@string/gender"
        app:layout_constraintEnd_toEndOf="@id/change_gender_desc"
        app:layout_constraintStart_toStartOf="@id/change_gender_desc"
        app:layout_constraintTop_toBottomOf="@id/change_gender_desc" />


    <com.google.android.material.card.MaterialCardView
        android:id="@+id/male_cardView"
        android:layout_width="0dp"
        android:layout_height="64dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="17dp"
        android:checkable="true"
        android:clickable="true"
        android:focusable="true"
        app:cardElevation="0dp"
        app:cardForegroundColor="@color/transparent"
        app:cardBackgroundColor="@color/background"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintEnd_toStartOf="@+id/female_cardView"
        app:layout_constraintStart_toStartOf="@+id/textView42"
        app:layout_constraintTop_toBottomOf="@+id/textView42"
        app:rippleColor="@color/transparent"
        app:strokeColor="@color/gender_male_stroke_selected"
        app:cardCornerRadius="12dp"
        app:strokeWidth="1dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/transparent">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/appCompatImageView3"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginStart="16dp"
                android:scaleType="centerInside"
                android:tint="@color/gender_male_color_icon_selector"
                app:layout_constraintBottom_toBottomOf="@+id/textView43"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/textView43"
                app:srcCompat="@drawable/ic_male" />

            <TextView
                android:id="@+id/textView43"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:text="@string/male"
                android:textColor="@color/gender_male_text_color_selector"
                style="@style/text_style_100"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/appCompatImageView3"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/female_cardView"
        android:layout_width="0dp"
        android:layout_height="64dp"
        android:checkable="true"
        android:clickable="true"
        android:focusable="true"
        app:cardElevation="0dp"
        app:cardForegroundColor="@color/transparent"
        app:cardBackgroundColor="@color/background"
        app:layout_constraintBottom_toBottomOf="@+id/male_cardView"
        app:layout_constraintEnd_toEndOf="@+id/change_gender_desc"
        app:layout_constraintStart_toEndOf="@+id/male_cardView"
        app:layout_constraintTop_toTopOf="@+id/male_cardView"
        app:rippleColor="@color/transparent"
        app:strokeColor="@color/gender_female_color_selector"
        app:cardCornerRadius="12dp"
        app:strokeWidth="1dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/appCompatImageView32"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:scaleType="centerInside"
                android:layout_marginStart="16dp"
                android:tint="@color/gender_female_color_icon_selector"
                app:layout_constraintBottom_toBottomOf="@+id/textView44"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/textView44"
                app:srcCompat="@drawable/ic_female" />

            <TextView
                android:id="@+id/textView44"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:text="@string/female"
                android:textColor="@color/gender_female_text_color_selector"
                style="@style/text_style_100"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/appCompatImageView32"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>

    <ImageView
        android:id="@+id/attention_icon"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="40dp"
        android:src="@drawable/ic_info_1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/male_cardView" />

    <TextView
        android:id="@+id/attention_txt"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:textColor="@color/description_primary"
        style="@style/text_style_100"
        app:layout_constraintEnd_toEndOf="@+id/female_cardView"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toEndOf="@id/attention_icon"
        app:layout_constraintTop_toTopOf="@id/attention_icon"
        android:text="@string/note_you_can_only_change_your_gender_once_in_every_six_months_an"
        />
    <com.duaag.android.views.DuaButton
        app:buttonType="PrimaryWithState"
        android:id="@+id/btn_save"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:stateListAnimator="@null"
        android:layout_marginTop="32dp"
        android:layout_marginBottom="24dp"
        android:layout_marginHorizontal="24dp"
        android:text="@string/save_information"
        android:enabled="false"
        android:textAllCaps="false"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>
