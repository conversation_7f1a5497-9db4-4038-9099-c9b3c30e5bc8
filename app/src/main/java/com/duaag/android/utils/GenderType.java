package com.duaag.android.utils;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019-09-09.
 */
public enum GenderType {
    MAN("M"),
    WOMAN("F");

    private final String value;

    GenderType(final String newValue) {
        value = newValue;
    }

    public String getValue() {
        return value;
    }

    public static GenderType fromValue(String value) {
        for (GenderType genderType : GenderType.values()) {
            if (genderType.getValue().equals(value)) {
                return genderType;
            }
        }
        return null;
    }
}
