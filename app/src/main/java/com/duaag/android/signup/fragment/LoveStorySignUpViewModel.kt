package com.duaag.android.signup.fragment

import android.content.Context
import androidx.annotation.Keep
import androidx.annotation.StringRes
import androidx.lifecycle.ViewModel
import com.duaag.android.BuildConfig
import com.duaag.android.R
import com.duaag.android.utils.YouTubeUtil
import com.duaag.android.utils.getStringResourceByName
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

class LoveStorySignUpViewModel @Inject constructor() : ViewModel() {


    private val _dataUi: MutableStateFlow<LoveStorySignUpUiData> = MutableStateFlow(LoveStorySignUpUiData())
    val dataUi: StateFlow<LoveStorySignUpUiData> get() = _dataUi.asStateFlow()

}

val reviews = listOf(
    Review.Item(
        userName = R.string.user_name_first_review,
        reviewText = R.string.user_first_review_desc
    ),
    Review.Item(
        userName = R.string.user_name_second_review,
        reviewText = R.string.user_second_review_desc
    ),
    Review.Item(
        userName = R.string.user_name_third_review,
        reviewText = R.string.user_third_review_desc
    ),
    Review.Item(
        userName = R.string.user_name_fourth_review,
        reviewText = R.string.user_fourth_review_desc
    )
)

data class LoveStorySignUpUiData(
    val videoUrlKey: String = BuildConfig.LOVE_STORY_LINK_KEY,
    val stories: List<Review> = reviews,
) {
    fun getThumbnailUrl(context: Context): String? =
        YouTubeUtil.getThumbnailUrl(getVideoUrl(context))

    fun getVideoUrl(context: Context) = context.getStringResourceByName(videoUrlKey)
}

@Keep
sealed class Review {
    data class Header(
        val thumbnailUrl: String? = null,
        val videoUrl: String? = null
    ) : Review()

    data class Item(
        @StringRes val userName: Int,
        @StringRes val reviewText: Int
    ) : Review()
}