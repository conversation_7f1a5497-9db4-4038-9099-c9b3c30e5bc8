package com.duaag.android.signup.fragment


import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.core.content.ContextCompat
import androidx.core.view.children
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapLogOutTypeValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentGenderScreenBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.logSignUpEvent
import com.duaag.android.signup.SignUpActivity
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.signup.signup_persist.domain.models.SignUpPersistStepsEnum
import com.duaag.android.signup.viewmodel.SharedSignUpViewModel
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.convertDpToPx
import com.duaag.android.utils.navigateSafer
import com.duaag.android.utils.updateLocale
import com.duaag.android.uxcam.sendUxCamEvent
import com.google.android.material.card.MaterialCardView
import javax.inject.Inject


class GenderFragment : Fragment(), AccountCreatedDialogFragment.AccountCreatedInterface {

    @Inject
    lateinit var duaAccount: DuaAccount

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val sharedViewModel by viewModels<SharedSignUpViewModel>({ activity as SignUpActivity }) { viewModelFactory }
    private var _binding: FragmentGenderScreenBinding? = null
    private val binding get() = _binding!!

    private val genderCardChangeListener by lazy {
        MaterialCardView.OnCheckedChangeListener { card, isChecked ->
            card.checkedIcon = ContextCompat.getDrawable(requireContext(),R.drawable.transparent_drawable)
            card.strokeWidth = if(isChecked) convertDpToPx(2f).toInt() else  convertDpToPx(1f).toInt()
            if (isChecked) {
                card.isSelected = true
                card.children.forEach {
                    it.isSelected = true
                }

            } else {
                card.isSelected = false
                card.children.forEach {
                    it.isSelected = false
                }
            }
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SignUpActivity).signUpComponent.inject(this)
    }

    private val pressedCallback: OnBackPressedCallback = object : OnBackPressedCallback(true /* enabled by default */) {
        override fun handleOnBackPressed() {
            val dialog = AccountCreatedDialogFragment.newInstance()
            dialog.show(childFragmentManager, "accountCreated")
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        requireActivity().onBackPressedDispatcher.addCallback(this, pressedCallback)

        if(!duaAccount.duaSharedPrefs.getAuthMethode().isNullOrEmpty()) {
            val auth = when (duaAccount.duaSharedPrefs.getAuthMethode()) {
                AuthMethod.EMAIL.methodName -> AuthMethod.EMAIL
                AuthMethod.PHONE.methodName -> AuthMethod.PHONE
                AuthMethod.GOOGLE.methodName -> AuthMethod.GOOGLE
                else -> AuthMethod.FACEBOOK
            }
            sharedViewModel.setAuthMethod(auth)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        _binding = FragmentGenderScreenBinding.inflate(inflater, container, false)
        val view = binding.root
        if (sharedViewModel.shouldSkipSignUpPersistStep(SignUpPersistStepsEnum.GENDER)) {
            sharedViewModel.setSignUpPersistStepAsSkipped(SignUpPersistStepsEnum.GENDER)
            findNavController().navigate(R.id.action_genderFragment_to_signUpNameFragment2)
        } else {
            sendClevertapEvent(
                ClevertapEventEnum.GENDER_SCREENVIEW, mapOf(
                    ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedViewModel.authMethod?.methodName))
            sendUxCamEvent(
                ClevertapEventEnum.GENDER_SCREENVIEW, mapOf(
                    ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to sharedViewModel.authMethod?.methodName))
        }

        initView(genderCardChangeListener)

        sharedViewModel.gender.observe(viewLifecycleOwner) {
            when (it) {
                GenderType.MAN -> {
                    modifyGenderMaleCard(binding.maleCardView)
                }
                GenderType.WOMAN -> {
                    modifyGenderFemaleCard(binding.femaleCardView)
                }
                else -> {
                }
            }
        }

        return view
    }

    private fun navigateToNextScreen() {
        logSignUpEvent(sharedViewModel.authMethod, FirebaseAnalyticsEventsName.GENDER)

        findNavController().navigateSafer(R.id.action_genderFragment_to_signUpNameFragment2)
    }


    private fun modifyGenderFemaleCard(femaleCardView: MaterialCardView) {
        // Only toggle if not already checked
        if (!femaleCardView.isChecked) {
            femaleCardView.isChecked = true
            binding.maleCardView.isChecked = false
            sharedViewModel.setGender(GenderType.WOMAN)
        }
    }

    private fun modifyGenderMaleCard(maleCardView: MaterialCardView) {
        // Only toggle if not already checked
        if (!maleCardView.isChecked) {
            maleCardView.isChecked = true
            binding.femaleCardView.isChecked = false
            sharedViewModel.setGender(GenderType.MAN)
        }
    }

    private fun initView(genderCardListener: MaterialCardView.OnCheckedChangeListener) {
        binding.maleCardView.let {
            it.setOnCheckedChangeListener(genderCardListener)
            it.setOnClickListener {
                sharedViewModel.persistGender(GenderType.MAN)
                sharedViewModel.clearThirdPartyGenderPersistence()

                if (sharedViewModel.gender.value == GenderType.MAN) {
                    navigateToNextScreen()
                } else {
                    sharedViewModel.setGender(GenderType.MAN)
                    navigateToNextScreen()
                }
            }
        }
        binding.femaleCardView.let {
            it.setOnCheckedChangeListener(genderCardListener)
            it.setOnClickListener {
                sharedViewModel.persistGender(GenderType.WOMAN)
                sharedViewModel.clearThirdPartyGenderPersistence()

                if (sharedViewModel.gender.value == GenderType.WOMAN) {
                    navigateToNextScreen()
                } else {
                    sharedViewModel.setGender(GenderType.WOMAN)
                    navigateToNextScreen()
                }
            }
        }
    }

    override fun onSignOutClicked() {
        duaAccount.deleteUserDevice { result ->
            if (result) {
                logSignUpEvent(sharedViewModel.authMethod, FirebaseAnalyticsEventsName.LOG_OUT_BEFORE_CREATING_PROFILE)

                val eventPremiumType = getPremiumTypeEventProperty(sharedViewModel.userRepository.user.value)

                val logOutTypeValues = ClevertapLogOutTypeValues.BEFORE_CREATING_PROFILE.values

                sendClevertapEvent(
                    ClevertapEventEnum.LOG_OUT, mapOf(
                        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                        ClevertapEventPropertyEnum.LOG_OUT_TYPE.propertyName to logOutTypeValues))

                duaAccount.deleteAllData()
                activity?.finish()
            } else {
                ToastUtil.toast(resources.getString(R.string.smthg_went_wrong))
                logError(ErrorStatus.ON_SIGN_OUT_TO_SIGN_UP)
            }
        }
    }
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

}
