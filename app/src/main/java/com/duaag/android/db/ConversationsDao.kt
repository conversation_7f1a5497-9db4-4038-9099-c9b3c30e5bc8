package com.duaag.android.db

import androidx.room.*
import com.duaag.android.chat.adapters.InstaChatAdapter.Companion.SEVENTYTWOHOURSINMS
import com.duaag.android.chat.model.ConversationModel
import com.duaag.android.chat.model.ConversationToken
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged

/**
 * The Data Access Object for the ConversationModel class.
 */
@Dao
interface ConversationsDao {

    companion object{
        const val INSTACHAT_TYPE = "instachat"
    }

    @Query("SELECT * FROM conversations LEFT JOIN conversation_token ON id = conversation_token.conversationId WHERE id = :conversationId")
    fun getConversationsWithTokens(conversationId: String): Map<ConversationModel, ConversationToken?>

    @Query("SELECT * FROM conversations WHERE type NOT LIKE '$INSTACHAT_TYPE' order BY lastMessageTime DESC")
    fun getConversations(): List<ConversationModel>

    @Query("SELECT * FROM conversations WHERE type NOT LIKE '$INSTACHAT_TYPE' AND id=:conversationId LIMIT 1")
    fun getConversationById(conversationId: String): ConversationModel?

    @Query("SELECT * FROM conversations where name LIKE :name||'%' AND type NOT LIKE '$INSTACHAT_TYPE' order BY lastMessageTime DESC")
    fun getConversations(name: String) : Flow<List<ConversationModel>>

    fun getConversationsDistinctUntilChanged(name:String) =
            getConversations(name).distinctUntilChanged()

    @Query("SELECT * FROM conversations where type LIKE '$INSTACHAT_TYPE' AND (lastMessageTime+$SEVENTYTWOHOURSINMS) > :currentTime order BY lastMessageTime DESC ")
    fun getInstaChats(currentTime: Long): List<ConversationModel>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertConversations(users: List<ConversationModel>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertConversation(conversation: ConversationModel)

    @Query("DELETE FROM conversations WHERE type NOT LIKE '$INSTACHAT_TYPE'")
    suspend fun deleteConversations()

    @Query("DELETE FROM conversations WHERE type LIKE '$INSTACHAT_TYPE'")
    suspend fun deleteInstaChats()

    @Query("DELETE FROM conversations where userId = :id")
    suspend fun deleteConversationByUserId(id: String)

    @Query("DELETE FROM conversations where id = :id")
    suspend fun deleteConversationById(id: String)

    @Query("delete from conversations where id in (:idList)")
    fun deleteConversations(idList: List<String>)

    @Query("delete from conversation_token where conversationId in (:idList)")
    fun deleteConversationsTokens(idList: List<String>)

    @Query("delete from conversation_token where conversationId = :id")
    fun deleteConversationTokens(id: String)


    @Transaction
    suspend fun updateConversationData(conversations: List<ConversationModel>) {
        val items = getConversations()
        val failedItems = items.filter { it.hasFailed }
        conversations.forEach { con ->
            val hasItem = failedItems.find { it.id == con.id }
            if ( hasItem != null) {
                con.hasFailed = true
                con.lastMessageText = hasItem.lastMessageText
            }
        }
        deleteConversations()
        insertConversations(conversations)
    }

    @Transaction
    suspend fun updateInstaChatData(conversations: List<ConversationModel>) {
        val items = getConversations()
        val failedItems = items.filter { it.hasFailed }
        conversations.forEach { con ->
            val hasItem = failedItems.find { it.id == con.id }
            if ( hasItem != null) {
                con.hasFailed = true
                con.lastMessageText = hasItem.lastMessageText
            }
        }
        deleteInstaChats()
        insertConversations(conversations)
    }

    @Transaction
    suspend fun deleteConversationDataById(conversationId: String) {
        deleteConversationById(conversationId)
        deleteConversationTokens(conversationId)
    }

    @Transaction
    suspend fun deleteConversationsDataById(idList: List<String>) {
        deleteConversations(idList)
        deleteConversationsTokens(idList)
    }

}
