package com.duaag.android.db

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.duaag.android.base.models.CommunityInfo
import kotlinx.coroutines.flow.Flow

@Dao
interface CommunityInfoDao {

    @Query("SELECT EXISTS(SELECT 1 FROM community_info LIMIT 1)")
    suspend fun hasCommunities(): <PERSON>olean

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(communityInfo: CommunityInfo)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(communities: List<CommunityInfo>)

    @Query("SELECT * FROM community_info WHERE id = :id")
    fun getCommunityInfoById(id: String): CommunityInfo

    @Query("SELECT * FROM community_info")
    fun getAllCommunityInfo(): Flow<List<CommunityInfo>>

}