package com.duaag.android.home.fragments

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.FrameLayout
import androidx.appcompat.widget.SearchView
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.base.models.UpdateTagsModel
import com.duaag.android.clevertap.ClevertapVerificationSourceValues
import com.duaag.android.clevertap.sendAddInfoAddedEvent
import com.duaag.android.databinding.AddFilterItemBottomSheetBinding
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.adapter.FilterItemAdapter
import com.duaag.android.home.fragments.FilterOptionsFragment.Companion.ARG_EVENT_SOURCE
import com.duaag.android.home.models.FilterType
import com.duaag.android.home.viewmodels.AddFilterItemsViewModel
import com.duaag.android.home.viewmodels.AddFilterItemsViewModelFactory
import com.duaag.android.home.viewmodels.FilterOptionsViewModel
import com.duaag.android.home.viewmodels.FilterOptionsViewModelFactory
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.home.viewmodels.provideAddFilterItemsViewModelFactory
import com.duaag.android.home.viewmodels.provideFactory
import com.duaag.android.utils.hideKeyboard
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.updateLocale
import com.duaag.android.utils.vibrateTap
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import kotlinx.coroutines.launch
import javax.inject.Inject


class FilterItemTagPickerBottomSheet : BottomSheetDialogFragment() {

    lateinit var filterType: FilterType
    private var isProfileEdit: Boolean = false
    private var _binding: AddFilterItemBottomSheetBinding? = null
    private val binding get() = _binding!!



    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory

    @Inject
    lateinit var addFilterItemsViewModelFactory: AddFilterItemsViewModelFactory
    @Inject
    lateinit var filterOptionsViewModelFactory: FilterOptionsViewModelFactory

    private val homeViewModel by viewModels<HomeViewModel>({ activity as HomeActivity} ) { viewModelFactory }
    private val filterOptionsViewModel by navGraphViewModels<FilterOptionsViewModel>(R.id.nav_graph_filter) {
        provideFactory(
            filterOptionsViewModelFactory,
            requireArguments().getString(ARG_EVENT_SOURCE) ?: "",
        ) }
    private val addFilterItemsViewModel by viewModels<AddFilterItemsViewModel> {
        provideAddFilterItemsViewModelFactory(
            addFilterItemsViewModelFactory,
            initialItems = arguments?.getIntegerArrayList(CHOSEN_ITEMS) ?: emptyList(),
            filterType = arguments?.getSerializable(FILTER_TYPE) as FilterType,
            filterExtended = arguments?.getBoolean(FILTER_EXTENDED) ?: false,
            profileEdit = arguments?.getBoolean(PROFILE_EDIT) ?: false,
        )
    }


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as HomeActivity).homeComponent.inject(this)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState) as BottomSheetDialog
        dialog.setOnShowListener { dialogInterface ->
            val d = dialogInterface as BottomSheetDialog
            val bottomSheet =
                d.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout?
            bottomSheet?.let {
                val behavior = BottomSheetBehavior.from(it)
                behavior.state = BottomSheetBehavior.STATE_EXPANDED

                if(filterType == FilterType.LANGUAGES) {
                    behavior.peekHeight = WindowManager.LayoutParams.MATCH_PARENT
                    bottomSheet.layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT
                }
            }
        }
        return dialog
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        filterType = arguments?.getSerializable(FILTER_TYPE) as FilterType
        isProfileEdit = arguments?.getBoolean(PROFILE_EDIT) ?: false
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = AddFilterItemBottomSheetBinding.inflate(inflater, container, false)
        dialog!!.window!!.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)

        when(filterType) {
            FilterType.LOOKING_FOR -> {
                binding.title.text = getString(R.string.looking_for)
                hideOtherViews()
            }
            FilterType.LANGUAGES -> {
                if(isProfileEdit) {
                    binding.title.text = getString(R.string.languages_cards)
                } else {
                    binding.title.text = getString(R.string.languages)
                    val limit = homeViewModel.userProfile.value?.settings?.filters?.limits?.languages
                    binding.description.text = getString(R.string.choose_up_to_languages_desc_an, limit.toString())
                }
            }
            FilterType.RELIGION -> {
                if(isProfileEdit) {
                    binding.title.text = getString(R.string.what_is_your_religion)
                    hideOtherViews()
                } else {
                    binding.title.text = getString(R.string.religion)
                    val limit = homeViewModel.userProfile.value?.settings?.filters?.limits?.religions
                    binding.description.text = getString(R.string.choose_up_to_religions_desc_an, limit.toString())
                }
            }

            else -> {}
        }

        binding.showAnyoneContainer.isVisible = !isProfileEdit

        addFilterItemsViewModel.fetchTagsIfNotCached()

        setCallbacks()

        initRecyclerViews()

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                addFilterItemsViewModel.addFilterItemsUiState.collect { state ->
                    toggleLoadingVisibility(state.isLoading)

                    val chosenIdsSet = state.chosenTagsList.map { it }.toSet()

                    val updatedList = state.tagList.map { tag ->
                        tag.copy(isChecked = tag.id in chosenIdsSet.map { it.id })
                    }

                    if (updatedList.isNotEmpty()) {
                        binding.somethingWentWrongContainer.root.visibility = View.GONE
                        if(binding.search.query!=null) {
                            queryItems(binding.search.query.toString())
                        } else {
                            (binding.list.adapter as FilterItemAdapter).setData(updatedList)
                        }
                    } else {
                        binding.somethingWentWrongContainer.root.visibility = View.VISIBLE
                        logError(ErrorStatus.FILTER_ITEM_TAG_PICKER_BOTTOM_SHEET)
                    }

                    val limits = homeViewModel.userProfile.value?.settings?.filters?.limits
                    val limit = when(filterType) {
                        FilterType.LOOKING_FOR -> if(isProfileEdit) 1 else limits?.lookingFor ?: 7
                        FilterType.RELIGION -> if(isProfileEdit) 1 else limits?.religions ?: 3
                        FilterType.LANGUAGES -> if(isProfileEdit) 7 else limits?.languages ?: 7
                        else -> 1
                    }
                    val selectedText = getString(R.string.selected_counter_an, state.chosenTagsList.size.toString(), limit.toString())
                    binding.selectedCount.text = selectedText

                    val haveTheItemsChanged = addFilterItemsViewModel.haveTheItemsChanged()
                    binding.applyBtn.isEnabled = haveTheItemsChanged

                    binding.showAnyoneSwitch.isChecked = state.filterExtended
                    binding.showAnyoneSwitch.isEnabled = state.chosenTagsList.isNotEmpty()
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                addFilterItemsViewModel.editProfileUiState.collect { state ->
                    if(state.success == true) {
                        val chosenTags = addFilterItemsViewModel.addFilterItemsUiState.value.chosenTagsList
                        when(filterType) {
                            FilterType.LOOKING_FOR -> {
                                filterOptionsViewModel.updateUserLookingFar(chosenTags)
                            }
                            FilterType.LANGUAGES -> {
                                filterOptionsViewModel.updateUserLanguages(chosenTags)
                            }
                            FilterType.RELIGION -> {
                                filterOptionsViewModel.updateUserReligion(chosenTags)
                            }

                            else -> {}
                        }

                        sendAddInfoAddedEvent(
                            filterType = filterType,
                            userModel = homeViewModel.userProfile.value,
                            ClevertapVerificationSourceValues.ADVANCED_FILTERS
                        )

                        dismissAllowingStateLoss()
                    }

                    toggleLoadingVisibility(state.isLoading)
                }
            }
        }


        return binding.root
    }

    fun hideOtherViews() {
        binding.search.visibility = View.GONE
        binding.selectedCount.visibility = View.GONE
        binding.resetBtn.visibility = View.GONE
        binding.divider.visibility = View.GONE
        binding.description.visibility = View.GONE
    }

    private fun setCallbacks() {
        binding.somethingWentWrongContainer.tryAgainBtn.setOnClickListener {
            binding.somethingWentWrongContainer.root.visibility = View.GONE
            homeViewModel.userProfile.value?.let { homeViewModel.getAllTags(it) }
        }

        binding.resetBtn.setOnSingleClickListener {
            addFilterItemsViewModel.resetTags()
        }

        binding.applyBtn.setOnSingleClickListener {
            if(isProfileEdit) {
                val tagTypeId = when(filterType) {
                    FilterType.LOOKING_FOR -> 6
                    FilterType.RELIGION -> 4
                    FilterType.LANGUAGES -> 2
                    else -> 0
                }
                val chosenItems = addFilterItemsViewModel.addFilterItemsUiState.value.chosenTagsList.map { it.id }
                val updateTagModel = mapToUpdateTagsModel(tagTypeId, chosenItems)
                addFilterItemsViewModel.applyProfileChanges(updateTagModel)
            } else {
                val tags = addFilterItemsViewModel.addFilterItemsUiState.value.chosenTagsList.map { it.id }
                val filterException = addFilterItemsViewModel.addFilterItemsUiState.value.filterExtended
                when(filterType) {
                    FilterType.LOOKING_FOR -> {
                        filterOptionsViewModel.updateLookingFar(tags, filterException)
                    }
                    FilterType.LANGUAGES -> {
                        filterOptionsViewModel.updateLanguages(tags, filterException)
                    }
                    FilterType.RELIGION -> {
                        filterOptionsViewModel.updateReligion(tags, filterException)
                    }

                    else -> {}
                }

                dismissAllowingStateLoss()
            }
           
        }

        binding.search.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                queryItems(query.toString())
                binding.list.scrollToPosition(0)

                hideKeyboard()
                return false
            }

            override fun onQueryTextChange(newText: String?): Boolean {
                queryItems(newText.toString())
                binding.list.scrollToPosition(0)

                return false
            }
        })

        binding.showAnyoneSwitch.setOnCheckedChangeListener { buttonView, isChecked ->
            addFilterItemsViewModel.setCheckedState(isChecked)
        }
    }

    private fun initRecyclerViews() {
        val layoutParams = binding.list.layoutParams
        var maxItems = 0
        when(filterType) {
            FilterType.LOOKING_FOR -> {
                maxItems = homeViewModel.userProfile.value?.settings?.filters?.limits?.lookingFor ?: 3
                layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT
            }
            FilterType.LANGUAGES -> {
                maxItems = homeViewModel.userProfile.value?.settings?.filters?.limits?.languages ?: 7
            }
            FilterType.RELIGION -> {
                maxItems = homeViewModel.userProfile.value?.settings?.filters?.limits?.religions ?: 7
                layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT
            }

            else -> {}
        }
        binding.list.layoutParams = layoutParams
        binding.list.requestLayout()

        val filterAdapter = FilterItemAdapter(
            clickListener = FilterItemAdapter.ItemFilterClickListener { item ->
                val newItem = item.copy(isChecked = !item.isChecked)

                if((filterType == FilterType.LOOKING_FOR || filterType == FilterType.RELIGION) && isProfileEdit) {
                    addFilterItemsViewModel.onSingleItemClick(newItem)
                } else {
                    val checkedCount = addFilterItemsViewModel.getCheckedCount()
                    if(checkedCount >= maxItems && !item.isChecked) {
                        binding.root.context.vibrateTap()
                        return@ItemFilterClickListener
                    }

                    addFilterItemsViewModel.onItemClick(newItem)
                }
            },
            filterType = filterType,
            isProfileEdit = isProfileEdit
        )

        binding.list.layoutManager = LinearLayoutManager(requireContext(), RecyclerView.VERTICAL, false)

        binding.list.adapter = filterAdapter
    }

    private fun queryItems(query: String) {
        val ethnicityUiState = addFilterItemsViewModel.addFilterItemsUiState.value
        val chosenIdsSet = ethnicityUiState.chosenTagsList.map { it }.toSet()

        if (query.isEmpty()) {
            val updatedList = ethnicityUiState.tagList.map { ethnicity ->
                ethnicity.copy(isChecked = ethnicity.id in chosenIdsSet.map { it.id })
            }

            val adapter = binding.list.adapter as FilterItemAdapter
            adapter.setData(updatedList)
        } else {
            val filteredData = ethnicityUiState.tagList.filter { it.tagName.contains(query, ignoreCase = true) }
                .map { ethnicity ->
                    ethnicity.copy(isChecked = ethnicity.id in chosenIdsSet.map { it.id })
                }

            val adapter = binding.list.adapter as FilterItemAdapter
            adapter.setData(filteredData)
        }
    }

    private fun toggleLoadingVisibility(showLoading: Boolean = false) = if(showLoading) {
        binding.applyBtn.visibility = View.INVISIBLE
        binding.progressBar.visibility = View.VISIBLE
    } else {
        binding.applyBtn.visibility = View.VISIBLE
        binding.progressBar.visibility = View.GONE
    }

    override fun onDestroyView() {
        dialog!!.window!!.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)

        super.onDestroyView()

        _binding = null
    }

    fun mapToUpdateTagsModel(typeId: Int, itemIds: List<Int>): UpdateTagsModel {
        return UpdateTagsModel(
            typeId = typeId,
            itemIds = itemIds.map { it }
        )
    }

    companion object {
        const val FILTER_TYPE = "filter_type"
        const val CHOSEN_ITEMS = "chosen_items"
        const val FILTER_EXTENDED = "filter_extended"
        const val PROFILE_EDIT = "profile_edit"

        fun newInstance(filterType: FilterType): FilterItemTagPickerBottomSheet {
            val fragment = FilterItemTagPickerBottomSheet()
            val args = Bundle()
            args.putSerializable(FILTER_TYPE, filterType)
            fragment.arguments = args
            return fragment
        }
    }
}
