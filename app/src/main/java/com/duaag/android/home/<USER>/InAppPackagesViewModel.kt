package com.duaag.android.home.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.duaag.android.home.models.InAppPackageModel
import com.duaag.android.premium_subscription.PremiumActivity
import com.duaag.android.utils.livedata.SingleLiveData
import io.purchasely.ext.Purchasely
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject


class InAppPackagesViewModel @Inject constructor() : ViewModel(){

    companion object {
        const val TAG = "InAppPackagesViewModelTag"

        const val impressionsProductId = "ImpressionsIn-AppPurchase"
        const val flightsProductId = "FlightIn-AppPurchase"
        const val boostsProductId = "boost_2025"
        const val instachatsProductId = "instachat_2025"
        const val undosProductId = "UndoIn-AppPurchase"
        const val premiumProductId = "PremiumSubscriptions"

        const val impressionsFirstPackage = "impressions_package_1_android"
        const val flightFirstPackage = "flight_package_1_android"
        const val boostFirstPackage = "1_boost"
        const val instachatFirstPackage = "3_instachat"
        const val undoFirstPackage = "undo_package_1_android"

    }


    var offerPackage: InAppPackageModel? = null

    var firstPackage: InAppPackageModel? = null
    var secondPackage: InAppPackageModel? = null
    var thirdPackage: InAppPackageModel? = null


    private val _planPrices: SingleLiveData<Void> = SingleLiveData()
    val planPrices: LiveData<Void>
        get() = _planPrices


    fun getPLYPlans(planIds: List<String>) {
        if (firstPackage != null || secondPackage != null || thirdPackage != null)
            return

        viewModelScope.launch(CoroutineExceptionHandler { _, exception ->
            Timber.tag(PremiumActivity.PURCHASELY_TAG).e("CoroutineExceptionHandler ${exception.message}")
            exception.printStackTrace()
        }) {
            val plyPlans = Purchasely.allProducts().map { it.plans }.flatten()
            plyPlans.let { plyPLans ->
                try{
                    if(planIds.size == 1) {
                        val offer = plyPLans.first { it.vendorId == planIds[0] }
                        offerPackage = InAppPackageModel(offer.vendorId!!, offer)
                    } else {
                        val first = plyPLans.first { it.vendorId == planIds[0] }
                        val second = plyPLans.first { it.vendorId == planIds[1] }
                        val third = plyPLans.first { it.vendorId == planIds[2] }

                        firstPackage = InAppPackageModel(first.vendorId!!, first)
                        secondPackage = InAppPackageModel(second.vendorId!!, second)
                        thirdPackage = InAppPackageModel(third.vendorId!!, third)
                    }
                }catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            _planPrices.call()
        }
    }
}