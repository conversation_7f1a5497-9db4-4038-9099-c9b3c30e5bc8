package com.duaag.android.home.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.databinding.DeletableItemFilterBinding
import com.duaag.android.home.models.FilterTagUiModel

class ChosenItemAdapter(private val clickListener: ItemFilterClickListener) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {


    private var items: ArrayList<FilterTagUiModel> = ArrayList()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        val binding = DeletableItemFilterBinding.inflate(layoutInflater, parent, false)
        return SingleViewHolder(binding)
    }

    fun setData(data: List<FilterTagUiModel>) {
        val diffCallback = ItemDiffUtil(items, data)
        val diffResult = DiffUtil.calculateDiff(diffCallback)
        items.clear()
        items.addAll(data)
        diffResult.dispatchUpdatesTo(this)
    }

    override fun getItemCount() = items.size

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        (holder as SingleViewHolder).bind(items[position])
    }

    inner class SingleViewHolder(val binding: DeletableItemFilterBinding) : RecyclerView.ViewHolder(binding.root) {
        init {
            binding.closeBtn.setOnClickListener {
                if(bindingAdapterPosition < 0)
                    return@setOnClickListener

                val model = items[bindingAdapterPosition]

                clickListener.onClick(model)
            }
        }
        fun bind(model: FilterTagUiModel) {
            val communityName = model.tagName
            binding.communityName.text = communityName
        }
    }


    class ItemDiffUtil(private val oldList: List<FilterTagUiModel>, private val newList: List<FilterTagUiModel>) : DiffUtil.Callback() {

        override fun getOldListSize(): Int = oldList.size

        override fun getNewListSize(): Int = newList.size


        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition].id == newList[newItemPosition].id
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition] == newList[newItemPosition]
        }

        override fun getChangePayload(oldItemPosition: Int, newItemPosition: Int): Any? {
            return super.getChangePayload(oldItemPosition, newItemPosition)
        }
    }

    class ItemFilterClickListener(val clickListener: (model: FilterTagUiModel) -> Unit) {
        fun onClick(model: FilterTagUiModel) = clickListener(model)
    }
}