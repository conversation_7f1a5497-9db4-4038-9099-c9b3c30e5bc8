package com.duaag.android.home.adapter

import android.content.Context
import android.content.res.ColorStateList
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.base.models.TagModel
import com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoFragment
import com.duaag.android.utils.MeasureHelper
import com.duaag.android.utils.dpToPx
import com.duaag.android.utils.imageCircle
import com.duaag.android.utils.newTypeFace
import com.duaag.android.views.MultipleSpanGridLayoutManager
import kotlin.properties.Delegates


class TagAdapter(private var tagList: List<TagModel>,private var fromHome: Boolean = false) : RecyclerView.Adapter<TagAdapter.Holder>() {

    /***
     * Measuring Helper which is used to measure each row of the recyclerView
     */
    private val measureHelper = MeasureHelper(this, tagList.size)

    /***
     * Attached RecyclerView to the Adapter
     */
    private var recyclerView: RecyclerView? = null

    /***
     * First step is to get the width of the recyclerView then
     * Proceed to measuring the holders.
     *
     * Is RecyclerView done measuring.
     */
    private var ready = false

    /***
     * Determines when the measuring of all the cells is done.
     * If the newVal is true the adapter should be updated.
     */
    var measuringDone by Delegates.observable(false) { _, _, newVal ->
        if (newVal)
            update()
    }

    /***
     * Called to update the adapter with the new LayoutManager.
     */
    private fun update() {

        recyclerView ?: return

        recyclerView?.apply {

            visibility = View.VISIBLE

            // Get the list of sorted items from measureHelper
            tagList = measureHelper.getItems()
            if(fromHome) {
                tagList = measureHelper.filteredItems
                notifyDataSetChanged()
            }
            // Get the list of sorted spans from measureHelper
            layoutManager = MultipleSpanGridLayoutManager(context, MeasureHelper.SPAN_COUNT, measureHelper.getSpans())
        }

    }

    /***
     * When recyclerView is attached measure the width and calculate the baseCell on measureHelper.
     */
    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)

        this.recyclerView = recyclerView.apply {

            visibility = View.INVISIBLE

            viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {

                    // Prevent the multiple calls
                    recyclerView.viewTreeObserver.removeOnGlobalLayoutListener(this)

                    // Measure the BaseCell on MeasureHelper.
                    measureHelper.measureBaseCell(recyclerView.width)

                    ready = true

                    // NotifyDataSet because itemCount value needs to update.
                    notifyDataSetChanged()
                }
            })
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
            Holder(LayoutInflater.from(parent.context).inflate(R.layout.tag_layout, parent, false))

    override fun getItemCount() = if (ready) tagList.size else 0

    override fun onBindViewHolder(holder: Holder, position: Int) {

        val tag = tagList[position]

        // Determine if the MeasureHelper is done measuring if not holder should be measured.
        val shouldMeasure = measureHelper.shouldMeasure()

        holder.setData(tag, shouldMeasure,fromHome)

        if (shouldMeasure)
            measureHelper.measure(holder, tag, if (fromHome) recyclerView else null)
    }

    class Holder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        fun setData(tag: TagModel, shouldMeasure: Boolean, fromHome: Boolean) {
            val backgroundId = if(fromHome) R.drawable.tag_not_common_home_solid else R.drawable.tag_not_common_solid
            val textColor = if(fromHome) R.color.gray_50 else R.color.title_primary
            val iconTint =if(fromHome) R.color.gray_50 else R.color.title_primary
            val fontFamily = if(fromHome) R.font.tt_norms_pro_medium else R.font.tt_norms_pro_normal

            val container = itemView.findViewById<ViewGroup>(R.id.tag_container)
            container.apply {
                val title = findViewById<TextView>(R.id.title)
                val icon = findViewById<ImageView>(R.id.icon)
                val flag = findViewById<ImageView>(R.id.flag)
                com.duaag.android.utils.setVisibility(flag,tag.tagTypeId == TagModel.COMMUNITY_CONSTANT)
                title.text = tag.title.replaceFirstChar { it.uppercase() }
                icon.imageTintList = ColorStateList.valueOf(ContextCompat.getColor(context, iconTint))
                title.setTextColor(ContextCompat.getColor(context, textColor))
                title.newTypeFace(fontFamily)
                background = ContextCompat.getDrawable(context, backgroundId)
                val tagIcon: Int? = when (tag.tagTypeId) {
                    AdditionalInfoFragment.AdditionalInfoType.HAVE_CHILDREN.typeId -> {
                        R.drawable.ic_childrens
                    }
                    AdditionalInfoFragment.AdditionalInfoType.WANT_CHILDREN.typeId -> {
                        R.drawable.want_children_icon_small
                    }
                    AdditionalInfoFragment.AdditionalInfoType.LOOKING_FOR.typeId -> {
                        R.drawable.ic_search
                    }
                    AdditionalInfoFragment.AdditionalInfoType.PETS.typeId -> {
                        R.drawable.ic_pets
                    }
                    AdditionalInfoFragment.AdditionalInfoType.SMOKE.typeId -> {
                        R.drawable.ic_cigarette
                    }
                    AdditionalInfoFragment.AdditionalInfoType.RELIGION.typeId -> {
                        R.drawable.ic_religion
                    }
                    TagModel.ZODIAC_SIGN_CONSTANT -> R.drawable.ic_zodiac

                    TagModel.HEIGHT_CONSTANT -> R.drawable.ic_ruler

                    else -> null
                }
                if(tag.tagTypeId == TagModel.COMMUNITY_CONSTANT){
                    imageCircle(flag,tag.flag)
                }
                if (tagIcon != null) {
                    icon.visibility = View.VISIBLE
                    icon.setImageResource(tagIcon)
                    this.setPadding(dpToPx(6f),dpToPx(4f),dpToPx(12f),dpToPx(4f))
                } else {
                    icon.visibility = View.GONE
                    icon.setImageDrawable(null)
                    this.setPadding(dpToPx(12f),dpToPx(7f),dpToPx(12f),dpToPx(7f))
                }


                /* set the height to normal, because in the measureHelper in order to fit
                    as much holders as possible we shrink the view to height of 1 */
                layoutParams.height = LinearLayout.LayoutParams.WRAP_CONTENT
            }

            /* if the measuring is done set the width to fill the whole cell to avoid unwanted
                empty spaces between the cells */
            if (!shouldMeasure)
                container.layoutParams.width = LinearLayout.LayoutParams.MATCH_PARENT
        }
    }

    companion object {
        fun inflateTagView(tag: TagModel, context: Context): View {
            val backgroundId = R.drawable.tag_not_common_home_solid
            val textColor = R.color.gray_50
            val iconTint = R.color.gray_50
            val fontFamily = R.font.tt_norms_pro_medium

            val layoutInflater = LayoutInflater.from(context)
            val container = layoutInflater.inflate(R.layout.tag_layout, null)
            container.apply {

                // Set layout parameters with margins
                val layoutParams = ViewGroup.MarginLayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                ).apply {
                    setMargins(0, 0, dpToPx(8f), 0) // left, top, right, bottom
                }
                container.layoutParams = layoutParams

                val title = findViewById<TextView>(R.id.title)
                val icon = findViewById<ImageView>(R.id.icon)
                val flag = findViewById<ImageView>(R.id.flag)
                com.duaag.android.utils.setVisibility(flag,tag.tagTypeId == TagModel.COMMUNITY_CONSTANT)
                title.text = tag.title.replaceFirstChar { it.uppercase() }
                icon.imageTintList = ColorStateList.valueOf(ContextCompat.getColor(context, iconTint))
                title.setTextColor(ContextCompat.getColor(context, textColor))
                title.newTypeFace(fontFamily)
                background = ContextCompat.getDrawable(context, backgroundId)
                val tagIcon: Int? = when (tag.tagTypeId) {
                    AdditionalInfoFragment.AdditionalInfoType.HAVE_CHILDREN.typeId -> {
                        R.drawable.ic_childrens
                    }
                    AdditionalInfoFragment.AdditionalInfoType.WANT_CHILDREN.typeId -> {
                        R.drawable.want_children_icon_small
                    }
                    AdditionalInfoFragment.AdditionalInfoType.LOOKING_FOR.typeId -> {
                        R.drawable.ic_search
                    }
                    AdditionalInfoFragment.AdditionalInfoType.PETS.typeId -> {
                        R.drawable.ic_pets
                    }
                    AdditionalInfoFragment.AdditionalInfoType.SMOKE.typeId -> {
                        R.drawable.ic_cigarette
                    }
                    AdditionalInfoFragment.AdditionalInfoType.RELIGION.typeId -> {
                        R.drawable.ic_religion
                    }
                    TagModel.ZODIAC_SIGN_CONSTANT -> R.drawable.ic_zodiac

                    else -> null
                }
               if(tag.tagTypeId == TagModel.COMMUNITY_CONSTANT){
                   imageCircle(flag,tag.flag)
               }
                if (tagIcon != null) {
                    icon.visibility = View.VISIBLE
                    icon.setImageResource(tagIcon)
                } else {
                    icon.visibility = View.GONE
                    icon.setImageDrawable(null)
                }
                this.setPadding(dpToPx(12f),dpToPx(7f),dpToPx(12f),dpToPx(7f))
            }

            return container
        }
    }
}