package com.duaag.android.home.fragments

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.DialogFragment
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.base.models.UserModel
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapEventSourceValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.clevertap.updateNotificationPermissionStatusInCleverTap
import com.duaag.android.databinding.FragmentNotificationPermissionBinding
import com.duaag.android.firebase.NotificationHelper
import com.duaag.android.home.HomeActivity
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.NotificationsSourceValues
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.settings.fragments.notifications.pushnotification.models.PushNotificationBody
import com.duaag.android.settings.fragments.notifications.pushnotification.models.PushNotificationType
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.requestPushNotificationPermissionWithLauncher
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.uxcam.sendUxCamEvent
import timber.log.Timber
import javax.inject.Inject

class NotificationPermissionDialog : DialogFragment() {

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs
    @Inject
    lateinit var duaAccount: DuaAccount

    private var  _binding: FragmentNotificationPermissionBinding? = null
    private val binding get() = _binding

    private val requestPushNotificationPermissionLauncher  =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { granted: Boolean ->
            sendNotificationPopUpEvents(duaSharedPrefs,DuaAccount.user)
            if(granted){
                sendNotificationAllowedEvents(duaSharedPrefs,DuaAccount.user)
            }
            updateNotificationPermissionStatusInCleverTap(DuaApplication.instance.applicationContext,duaSharedPrefs)
        }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        (requireActivity() as HomeActivity).homeComponent.inject(this)
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogFullScreenStyle)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentNotificationPermissionBinding.inflate(inflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding?.notifyMeBtn?.setOnSingleClickListener{
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU && !shouldShowRequestPermissionRationale(android.Manifest.permission.POST_NOTIFICATIONS)) {
                requestPushNotificationPermissionWithLauncher(requestPushNotificationPermissionLauncher)
            } else {
                showDeviceNotificationScreen(duaSharedPrefs, duaAccount)
            }
                    dismissAllowingStateLoss()
        }

        binding?.btnClose?.setOnSingleClickListener{
            dismissAllowingStateLoss()
        }
        sendScreenViewEvents(duaSharedPrefs,DuaAccount.user)
    }

    private fun sendNotificationAllowedEvents(duaSharedPrefs: DuaSharedPrefs,user:UserModel?) {
        val community = duaSharedPrefs.getUserCommunityName()
        val premiumType = getPremiumTypeEventProperty(user)
        val eventSourceCT = ClevertapEventSourceValues.RETENTION_CLEVERTAP.value
        val eventSourceGA = NotificationsSourceValues.RETENTION_CLEVERTAP.value

        val params = mapOf(
            ClevertapEventPropertyEnum.COMMUNITY.propertyName to community,
            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType,
            ClevertapEventPropertyEnum.PERMISSION_SOURCE_SCREEN.propertyName to eventSourceCT
        )
        sendClevertapEvent(ClevertapEventEnum.PERMISSION_NOTIFICATIONS_ALLOWED, params)
        sendUxCamEvent(ClevertapEventEnum.PERMISSION_NOTIFICATIONS_ALLOWED, params)

        firebaseLogEvent(FirebaseAnalyticsEventsName.PERMISSION_NOTIFICATIONS_ALLOWED,
            mapOf(FirebaseAnalyticsParameterName.COMMUNITY.value to community,
                FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to premiumType,
                FirebaseAnalyticsParameterName.PERMISSION_SOURCE_SCREEN.value to eventSourceGA))

    }
    private fun sendNotificationPopUpEvents(duaSharedPrefs: DuaSharedPrefs,user:UserModel?) {
        val community = duaSharedPrefs.getUserCommunityName()
        val premiumType = getPremiumTypeEventProperty(user)
        val eventSourceCT = ClevertapEventSourceValues.RETENTION_CLEVERTAP.value
        val eventSourceGA = NotificationsSourceValues.RETENTION_CLEVERTAP.value

        sendClevertapEvent(ClevertapEventEnum.PERMISSION_NOTIFICATIONS_POPUP,
            mapOf(ClevertapEventPropertyEnum.COMMUNITY.propertyName to community,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType,
                ClevertapEventPropertyEnum.PERMISSION_SOURCE_SCREEN.propertyName to eventSourceCT))

        firebaseLogEvent(FirebaseAnalyticsEventsName.PERMISSION_NOTIFICATIONS_POPUP,
            mapOf(FirebaseAnalyticsParameterName.COMMUNITY.value to community,
                FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to premiumType,
                FirebaseAnalyticsParameterName.PERMISSION_SOURCE_SCREEN.value to eventSourceGA))

    }
    private fun sendScreenViewEvents(duaSharedPrefs: DuaSharedPrefs,user:UserModel?) {
        val community = duaSharedPrefs.getUserCommunityName()
        val premiumType = getPremiumTypeEventProperty(user)
        val eventSourceCT = ClevertapEventSourceValues.RETENTION_CLEVERTAP.value
        val eventSourceGA = NotificationsSourceValues.RETENTION_CLEVERTAP.value
        sendClevertapEvent(
            ClevertapEventEnum.PERMISSION_NOTIFICATIONS_SCREEN_VIEW,
            mapOf(
                ClevertapEventPropertyEnum.COMMUNITY.propertyName to community,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType,
                ClevertapEventPropertyEnum.PERMISSION_SOURCE_SCREEN.propertyName to eventSourceCT))

        firebaseLogEvent(
            FirebaseAnalyticsEventsName.PERMISSION_NOTIFICATIONS_SCREEN_VIEW,
            mapOf(
                FirebaseAnalyticsParameterName.COMMUNITY.value to community,
                FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to premiumType,
                FirebaseAnalyticsParameterName.PERMISSION_SOURCE_SCREEN.value to eventSourceGA))
    }
    private fun showDeviceNotificationScreen(duaSharedPrefs: DuaSharedPrefs, duaAccount: DuaAccount) {
        val isDeviceNotificationOn =
            NotificationHelper.isMessageNotificationsEnables(requireContext())
        val isSettingsNotificationOn = duaSharedPrefs.isPushMessageNotificationsEnables()
        if (!isSettingsNotificationOn) {
            val notification = duaSharedPrefs.getPushNotifications()
            for (pushNotificationModel in notification) {
                pushNotificationModel.isChecked = true
            }
            duaAccount.setNotifications(
                PushNotificationType.NEW_MESSAGE,
                PushNotificationBody.convertToPushNotificationBody(
                    notification
                )
            )
        }

        if (!isDeviceNotificationOn) {
            val intent = Intent()
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.O -> {
                    intent.action = Settings.ACTION_APP_NOTIFICATION_SETTINGS
                    intent.putExtra(Settings.EXTRA_APP_PACKAGE, requireActivity().packageName)
                }
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                    intent.action = "android.settings.APP_NOTIFICATION_SETTINGS"
                    intent.putExtra("app_package", requireActivity().packageName)
                    intent.putExtra("app_uid", requireActivity().applicationInfo.uid)
                }
                else -> {
                    intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
                    intent.addCategory(Intent.CATEGORY_DEFAULT)
                    intent.data = Uri.parse("package:" + requireActivity().packageName)
                }
            }

            startActivity(intent)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        fun newInstance(): NotificationPermissionDialog = NotificationPermissionDialog()
    }
}