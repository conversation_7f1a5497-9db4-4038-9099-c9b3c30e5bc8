package com.duaag.android.vision

import android.content.ContentResolver
import android.graphics.Bitmap
import android.net.Uri
import timber.log.Timber
import java.io.IOException
import kotlin.math.max

class ImageVisionInteractor(private val imageProcessors: List<VisionImageProcessor>) {

    companion object {
        const val TAG = "ImageVisionUtil"

        private const val SIZE_SCREEN = "w:screen" // Match screen width
        private const val SIZE_1024_768 = "w:1024" // ~1024*768 in a normal ratio
        private const val SIZE_640_480 = "w:640" // ~640*480 in a normal ratio


        private fun getTargetedWidthHeight(selectedSize: String): Pair<Int, Int> {
            val targetWidth: Int
            val targetHeight: Int
            when (selectedSize) {
//                SIZE_SCREEN -> {
//                    targetWidth = imageMaxWidth
//                    targetHeight = imageMaxHeight
//                }
                SIZE_640_480 -> {
                    targetWidth = 480
                    targetHeight = 640
                }
                SIZE_1024_768 -> {
                    targetWidth = 768
                    targetHeight = 1024
                }
                else -> throw IllegalStateException("Unknown size")
            }
            return Pair(targetWidth, targetHeight)
        }
    }

    fun detectInImage(
        imageUri: Uri?,
        contentResolver: ContentResolver) {
        imageProcessors.forEach { imageProcessor ->
            Timber.tag(TAG).d("Try reload and detect image")
            try {
                if (imageUri == null) {
                    throw Exception("imageUri is null")
                }


                val imageBitmap = BitmapUtils.getBitmapFromContentUri(contentResolver, imageUri)
                        ?: throw Exception("getBitmapFromContentUri returned null")

                // Get the dimensions of the image view
                val targetedSize = getTargetedWidthHeight(SIZE_1024_768)

                // Determine how much to scale down the image
                val scaleFactor: Float = max(
                        imageBitmap.width.toFloat() / targetedSize.first.toFloat(),
                        imageBitmap.height.toFloat() / targetedSize.second.toFloat()
                )
                val resizedBitmap = Bitmap.createScaledBitmap(
                        imageBitmap,
                        (imageBitmap.width / scaleFactor).toInt(),
                        (imageBitmap.height / scaleFactor).toInt(),
                        true
                )

                imageProcessor.processBitmap(resizedBitmap)
            } catch (e: IOException) {
                Timber.tag(TAG).e(e.message.toString())
            }
        }
    }

    fun stopImageProcessors() {
        for (processor in imageProcessors)
            processor.stop()
    }
}