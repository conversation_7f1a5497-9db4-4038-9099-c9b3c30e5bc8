package com.duaag.android.views

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.graphics.Typeface
import android.os.Parcel
import android.os.Parcelable
import android.util.AttributeSet
import android.view.View
import androidx.core.content.res.ResourcesCompat
import com.duaag.android.R

class RoundedProgressBar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0) : View(context, attrs,defStyleAttr) {
    private val ovalRect = RectF()

    private val backgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val progressPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    private var strokeWidth: Float = 10f
    private var strokeColor: Int = Color.BLUE
    private var textColor: Int = Color.BLACK
    private var textPadding: Float = 0f
    private var strokePathColor: Int = Color.BLACK
    private var textSize: Float = 14f
    private var progress: Float = 0f
    private var max: Float = 5f
    private val progressTextFormat = "%.0f/%.0f"

    init {
        isSaveEnabled = true
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.RoundedProgressBar)

        strokeWidth = typedArray.getDimension(R.styleable.RoundedProgressBar_strokeWidth, 10f)
        strokeColor = typedArray.getColor(R.styleable.RoundedProgressBar_strokeColor, Color.BLUE)
        strokePathColor = typedArray.getColor(R.styleable.RoundedProgressBar_strokePathColor,Color.GRAY)

        textColor = typedArray.getColor(R.styleable.RoundedProgressBar_textColor, Color.BLACK)
        textSize = typedArray.getDimension(R.styleable.RoundedProgressBar_textSize, 24f)
        textPadding = typedArray.getDimension(R.styleable.RoundedProgressBar_textPadding, 0f) // Read textPadding

        backgroundPaint.color = strokePathColor
        backgroundPaint.style = Paint.Style.STROKE
        backgroundPaint.strokeWidth = strokeWidth

        progressPaint.color = strokeColor
        progressPaint.style = Paint.Style.STROKE
        progressPaint.strokeWidth = strokeWidth

        textPaint.color = textColor
        textPaint.textSize = textSize

        val fontFamilyResId = typedArray.getResourceId(R.styleable.RoundedProgressBar_fontFamily, -1)
        if (fontFamilyResId != -1) {
            val fontFamily = ResourcesCompat.getFont(context, fontFamilyResId)
            fontFamily?.let {
                textPaint.typeface = Typeface.create(it, Typeface.NORMAL)
            }
        }

        typedArray.recycle()
    }


    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val centerX = width / 2f
        val centerY = height / 2f
        val radius = centerX.coerceAtMost(centerY) - strokeWidth / 2f

         ovalRect.set(centerX - radius, centerY - radius, centerX + radius, centerY + radius)

        canvas.drawCircle(centerX, centerY, radius, backgroundPaint)

        val sweepAngle = (progress / max) * 360
        canvas.drawArc(ovalRect, -90f, sweepAngle, false, progressPaint)

        val progressText = String.format(progressTextFormat, progress, max)
        val textWidth = textPaint.measureText(progressText)
        val textX = centerX - textWidth / 2 // Center text horizontally
        val textY = centerY + textSize / 4 // Adjust text vertical position
        canvas.drawText(progressText, textX, textY, textPaint)
    }



    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val progressText = String.format(progressTextFormat, progress, max)

        // Calculate the width required for the text
        val textWidth = textPaint.measureText(progressText)

        // Calculate the space needed for both text and progress bar
        val desiredWidth = (textWidth + textPadding + paddingLeft + paddingRight + strokeWidth * 2).toInt()
        val desiredHeight = (textWidth + textPadding+ paddingTop + paddingBottom + strokeWidth * 2).toInt()

        // Adjust the desired dimensions based on the mode and size hints
        val widthMode = MeasureSpec.getMode(widthMeasureSpec)
        val widthSize = MeasureSpec.getSize(widthMeasureSpec)
        val heightMode = MeasureSpec.getMode(heightMeasureSpec)
        val heightSize = MeasureSpec.getSize(heightMeasureSpec)

        val finalWidth = when (widthMode) {
            MeasureSpec.EXACTLY -> widthSize // Use the specified width
            MeasureSpec.AT_MOST -> minOf(desiredWidth, widthSize) // Respect width size hints
            else -> desiredWidth // Use the desired width as a default
        }

        val finalHeight = when (heightMode) {
            MeasureSpec.EXACTLY -> heightSize // Use the specified height
            MeasureSpec.AT_MOST -> minOf(desiredHeight, heightSize) // Respect height size hints
            else -> desiredHeight // Use the desired height as a default
        }

        setMeasuredDimension(finalWidth, finalHeight)
    }

    override fun onSaveInstanceState(): Parcelable? {
        val superState = super.onSaveInstanceState()
        val savedState = SavedState(superState)

        savedState.progress = progress
        savedState.max = max

        return savedState
    }
    override fun onRestoreInstanceState(state: Parcelable?) {
        if (state is SavedState) {
            super.onRestoreInstanceState(state.superState)

            progress = state.progress
            max = state.max
        } else {
            super.onRestoreInstanceState(state)
        }
    }
    fun setProgress(progress: Float) {
        this.progress = progress
        invalidate()
    }

    fun setMax(max: Float) {
        this.max = max
        invalidate()
    }

}

class SavedState : View.BaseSavedState {
    var progress: Float = 0f
    var max: Float = 0f

    constructor(superState: Parcelable?) : super(superState)

    private constructor(parcel: Parcel) : super(parcel) {
        progress = parcel.readFloat()
        max = parcel.readFloat()
    }

    override fun writeToParcel(out: Parcel, flags: Int) {
        super.writeToParcel(out, flags)
        out.writeFloat(progress)
        out.writeFloat(max)
    }

    companion object CREATOR : Parcelable.Creator<SavedState> {
        override fun createFromParcel(parcel: Parcel): SavedState {
            return SavedState(parcel)
        }

        override fun newArray(size: Int): Array<SavedState?> {
            return arrayOfNulls(size)
        }
    }
}