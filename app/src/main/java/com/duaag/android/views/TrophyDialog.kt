package com.duaag.android.views

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.graphics.Color
import android.graphics.Path
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.View
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.AppCompatImageButton
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.animation.doOnEnd
import androidx.fragment.app.DialogFragment
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.home.HomeActivity.Companion.INTERACTION_LIMIT
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent

class TrophyDialog : DialogFragment() {


    companion object {

        fun newInstance(interactionLimit: String): TrophyDialog {
            val fragment = TrophyDialog()
            val args = Bundle()
            args.putString(INTERACTION_LIMIT, interactionLimit ?: "+50")
            fragment.arguments = args
            return fragment
        }
    }

    // Use this instance of the interface to deliver action events
    internal var listener: TrophyDialogListener? = null
    private var interactionLimit: String? = "+50"

    /* The activity that creates an instance of this dialog fragment must
 * implement this interface in order to receive event callbacks.
 * Each method passes the DialogFragment in case the host needs to query it. */


    interface TrophyDialogListener {
        fun onTrophyDialogDismissed(dismissed: Boolean)
    }

    fun setListener(trophyDialogListener: TrophyDialogListener) {
        this.listener = trophyDialogListener
    }

    private lateinit var customLayout: View

    private fun createAnimations(
        rightSidePath: Path,
        leftSidePath: Path,
        layout: View
    ): List<ObjectAnimator> {
        val likeButton1 = layout.findViewById<AppCompatImageButton>(R.id.like_button1)
        val likeButton2 = layout.findViewById<AppCompatImageButton>(R.id.like_button2)
        val likeButton3 = layout.findViewById<AppCompatImageButton>(R.id.like_button3)
        val likeButton4 = layout.findViewById<AppCompatImageButton>(R.id.like_button4)
        val likeButton5 = layout.findViewById<AppCompatImageButton>(R.id.like_button5)
        val likeButton6 = layout.findViewById<AppCompatImageButton>(R.id.like_button6)
        val likeButton7 = layout.findViewById<AppCompatImageButton>(R.id.like_button7)
        val likeButton8 = layout.findViewById<AppCompatImageButton>(R.id.like_button8)
        val likeButton9 = layout.findViewById<AppCompatImageButton>(R.id.like_button9)
        val likeButton10 = layout.findViewById<AppCompatImageButton>(R.id.like_button10)
        val likeButton11 = layout.findViewById<AppCompatImageButton>(R.id.like_button11RightSide)
        val likeButton12 = layout.findViewById<AppCompatImageButton>(R.id.like_button12)


        val likeButton1LeftSide = layout.findViewById<AppCompatImageButton>(R.id.like_button11)
        val likeButton2LeftSide = layout.findViewById<AppCompatImageButton>(R.id.like_button22)
        val likeButton3LeftSide = layout.findViewById<AppCompatImageButton>(R.id.like_button33)
        val likeButton4LeftSide = layout.findViewById<AppCompatImageButton>(R.id.like_button44)
        val likeButton5LeftSide = layout.findViewById<AppCompatImageButton>(R.id.like_button55)
        val likeButton6LeftSide = layout.findViewById<AppCompatImageButton>(R.id.like_button66)
        val likeButton7LeftSide = layout.findViewById<AppCompatImageButton>(R.id.like_button77)
        val likeButton8LeftSide = layout.findViewById<AppCompatImageButton>(R.id.like_button88)
        val likeButton9LeftSide = layout.findViewById<AppCompatImageButton>(R.id.like_button99)
        val likeButton10LeftSide = layout.findViewById<AppCompatImageButton>(R.id.like_button1010)
        val likeButton11LeftSide = layout.findViewById<AppCompatImageButton>(R.id.like_button1111)
        val likeButton12LeftSide = layout.findViewById<AppCompatImageButton>(R.id.like_button1212)


        //animators for rightside
        val bezierAnimator1 =
            ObjectAnimator.ofFloat(likeButton1, View.X, View.Y, rightSidePath).apply {
                duration = 2400
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val fadeAnimator1 = ObjectAnimator.ofFloat(likeButton1, "alpha", 1f, 0f).apply {
            duration = 2400
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }

        val rotateAnimator1 = ObjectAnimator.ofFloat(likeButton1, "rotation", -360f, 0f).apply {
            duration = 2400
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }

        val bezierAnimator2 =
            ObjectAnimator.ofFloat(likeButton2, View.X, View.Y, rightSidePath).apply {
                duration = 2400
                startDelay = 200
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }

        val fadeAnimator2 = ObjectAnimator.ofFloat(likeButton2, "alpha", 1f, 0f).apply {
            duration = 2400
            startDelay = 200
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }

        val rotateAnimator2 = ObjectAnimator.ofFloat(likeButton2, "rotation", -360f, 0f).apply {
            duration = 2400
            startDelay = 200
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }

        val bezierAnimator3 =
            ObjectAnimator.ofFloat(likeButton3, View.X, View.Y, rightSidePath).apply {
                duration = 2400
                startDelay = 200 * 2
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }

        val fadeAnimator3 = ObjectAnimator.ofFloat(likeButton3, "alpha", 1f, 0f).apply {
            duration = 2400
            startDelay = 200 * 2
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }

        val rotateAnimator3 = ObjectAnimator.ofFloat(likeButton3, "rotation", -360f, 0f).apply {
            duration = 2400
            startDelay = 200 * 2
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }

        val bezierAnimator4 =
            ObjectAnimator.ofFloat(likeButton4, View.X, View.Y, rightSidePath).apply {
                duration = 2400
                startDelay = 200 * 3
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }

        val fadeAnimator4 = ObjectAnimator.ofFloat(likeButton4, "alpha", 1f, 0f).apply {
            duration = 2400
            startDelay = 200 * 3
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }

        val rotateAnimator4 = ObjectAnimator.ofFloat(likeButton4, "rotation", -360f, 0f).apply {
            duration = 2400
            startDelay = 200 * 3
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }

        val bezierAnimator5 =
            ObjectAnimator.ofFloat(likeButton5, View.X, View.Y, rightSidePath).apply {
                duration = 2400
                startDelay = 200 * 4
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }

        val fadeAnimator5 = ObjectAnimator.ofFloat(likeButton5, "alpha", 1f, 0f).apply {
            duration = 2400
            startDelay = 200 * 4
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }

        val rotateAnimator5 = ObjectAnimator.ofFloat(likeButton5, "rotation", -360f, 0f).apply {
            duration = 2400
            startDelay = 200 * 4
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }

        val bezierAnimator6 =
            ObjectAnimator.ofFloat(likeButton6, View.X, View.Y, rightSidePath).apply {
                duration = 2400
                startDelay = 200 * 5
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }

        val fadeAnimator6 = ObjectAnimator.ofFloat(likeButton6, "alpha", 1f, 0f).apply {
            duration = 2400
            startDelay = 200 * 5
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }

        val rotateAnimator6 = ObjectAnimator.ofFloat(likeButton6, "rotation", -360f, 0f).apply {
            duration = 2400
            startDelay = 200 * 5
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }

        val bezierAnimator7 =
            ObjectAnimator.ofFloat(likeButton7, View.X, View.Y, rightSidePath).apply {
                duration = 2400
                startDelay = 200 * 6
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val fadeAnimator7 = ObjectAnimator.ofFloat(likeButton7, "alpha", 1f, 0f).apply {
            duration = 2400
            startDelay = 200 * 6
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }

        val rotateAnimator7 = ObjectAnimator.ofFloat(likeButton7, "rotation", -360f, 0f).apply {
            duration = 2400
            startDelay = 200 * 6
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }

        val bezierAnimator8 =
            ObjectAnimator.ofFloat(likeButton8, View.X, View.Y, rightSidePath).apply {
                duration = 2400
                startDelay = 200 * 7
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val fadeAnimator8 = ObjectAnimator.ofFloat(likeButton8, "alpha", 1f, 0f).apply {
            duration = 2400
            startDelay = 200 * 7
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }

        val rotateAnimator8 = ObjectAnimator.ofFloat(likeButton8, "rotation", -360f, 0f).apply {
            duration = 2400
            startDelay = 200 * 7
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }
        val bezierAnimator9 =
            ObjectAnimator.ofFloat(likeButton9, View.X, View.Y, rightSidePath).apply {
                duration = 2400
                startDelay = 200 * 8
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val fadeAnimator9 = ObjectAnimator.ofFloat(likeButton9, "alpha", 1f, 0f).apply {
            duration = 2400
            startDelay = 200 * 8
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }

        val rotateAnimator9 = ObjectAnimator.ofFloat(likeButton9, "rotation", -360f, 0f).apply {
            duration = 2400
            startDelay = 200 * 8
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }
        val bezierAnimator10 =
            ObjectAnimator.ofFloat(likeButton10, View.X, View.Y, rightSidePath).apply {
                duration = 2400
                startDelay = 200 * 9
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val fadeAnimator10 = ObjectAnimator.ofFloat(likeButton10, "alpha", 1f, 0f).apply {
            duration = 2400
            startDelay = 200 * 9
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }

        val rotateAnimator10 = ObjectAnimator.ofFloat(likeButton10, "rotation", -360f, 0f).apply {
            duration = 2400
            startDelay = 200 * 9
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }
        val bezierAnimator11 =
            ObjectAnimator.ofFloat(likeButton11, View.X, View.Y, rightSidePath).apply {
                duration = 2400
                startDelay = 200 * 10
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val fadeAnimator11 = ObjectAnimator.ofFloat(likeButton11, "alpha", 1f, 0f).apply {
            duration = 2400
            startDelay = 200 * 10
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }

        val rotateAnimator11 = ObjectAnimator.ofFloat(likeButton11, "rotation", -360f, 0f).apply {
            duration = 2400
            startDelay = 200 * 10
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }
        val bezierAnimator12 =
            ObjectAnimator.ofFloat(likeButton12, View.X, View.Y, rightSidePath).apply {
                duration = 2400
                startDelay = 200 * 11
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val fadeAnimator12 = ObjectAnimator.ofFloat(likeButton12, "alpha", 1f, 0f).apply {
            duration = 2400
            startDelay = 200 * 11
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }

        val rotateAnimator12 = ObjectAnimator.ofFloat(likeButton12, "rotation", -360f, 0f).apply {
            duration = 2400
            startDelay = 200 * 11
            repeatCount = ObjectAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
        }
//animators for leftside
        val bezierAnimator1LeftSide =
            ObjectAnimator.ofFloat(likeButton1LeftSide, View.X, View.Y, leftSidePath).apply {
                duration = 2400
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val fadeAnimator1LeftSide =
            ObjectAnimator.ofFloat(likeButton1LeftSide, "alpha", 1f, 0f).apply {
                duration = 2400
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val rotateAnimator1LeftSide =
            ObjectAnimator.ofFloat(likeButton1LeftSide, "rotation", 360f, 0f).apply {
                duration = 2400
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val bezierAnimator2LeftSide =
            ObjectAnimator.ofFloat(likeButton2LeftSide, View.X, View.Y, leftSidePath).apply {
                duration = 2400
                startDelay = 200
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val fadeAnimator2LeftSide =
            ObjectAnimator.ofFloat(likeButton2LeftSide, "alpha", 1f, 0f).apply {
                duration = 2400
                startDelay = 200
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val rotateAnimator2LeftSide =
            ObjectAnimator.ofFloat(likeButton2LeftSide, "rotation", 360f, 0f).apply {
                duration = 2400
                startDelay = 200
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val bezierAnimator3LeftSide =
            ObjectAnimator.ofFloat(likeButton3LeftSide, View.X, View.Y, leftSidePath).apply {
                duration = 2400
                startDelay = 200 * 2
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val fadeAnimator3LeftSide =
            ObjectAnimator.ofFloat(likeButton3LeftSide, "alpha", 1f, 0f).apply {
                duration = 2400
                startDelay = 200 * 2
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val rotateAnimator3LeftSide =
            ObjectAnimator.ofFloat(likeButton3LeftSide, "rotation", 360f, 0f).apply {
                duration = 2400
                startDelay = 200 * 2
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val bezierAnimator4LeftSide =
            ObjectAnimator.ofFloat(likeButton4LeftSide, View.X, View.Y, leftSidePath).apply {
                duration = 2400
                startDelay = 200 * 3
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val fadeAnimator4LeftSide =
            ObjectAnimator.ofFloat(likeButton4LeftSide, "alpha", 1f, 0f).apply {
                duration = 2400
                startDelay = 200 * 3
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val rotateAnimator4LeftSide =
            ObjectAnimator.ofFloat(likeButton4LeftSide, "rotation", 360f, 0f).apply {
                duration = 2400
                startDelay = 200 * 3
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val bezierAnimator5LeftSide =
            ObjectAnimator.ofFloat(likeButton5LeftSide, View.X, View.Y, leftSidePath).apply {
                duration = 2400
                startDelay = 200 * 4
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val fadeAnimator5LeftSide =
            ObjectAnimator.ofFloat(likeButton5LeftSide, "alpha", 1f, 0f).apply {
                duration = 2400
                startDelay = 200 * 4
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val rotateAnimator5LeftSide =
            ObjectAnimator.ofFloat(likeButton5LeftSide, "rotation", 360f, 0f).apply {
                duration = 2400
                startDelay = 200 * 4
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val bezierAnimator6LeftSide =
            ObjectAnimator.ofFloat(likeButton6LeftSide, View.X, View.Y, leftSidePath).apply {
                duration = 2400
                startDelay = 200 * 5
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val fadeAnimator6LeftSide =
            ObjectAnimator.ofFloat(likeButton6LeftSide, "alpha", 1f, 0f).apply {
                duration = 2400
                startDelay = 200 * 5
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val rotateAnimator6LeftSide =
            ObjectAnimator.ofFloat(likeButton6LeftSide, "rotation", 360f, 0f).apply {
                duration = 2400
                startDelay = 200 * 5
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val bezierAnimator7LeftSide =
            ObjectAnimator.ofFloat(likeButton7LeftSide, View.X, View.Y, leftSidePath).apply {
                duration = 2400
                startDelay = 200 * 6
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val fadeAnimator7LeftSide =
            ObjectAnimator.ofFloat(likeButton7LeftSide, "alpha", 1f, 0f).apply {
                duration = 2400
                startDelay = 200 * 6
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val rotateAnimator7LeftSide =
            ObjectAnimator.ofFloat(likeButton7LeftSide, "rotation", 360f, 0f).apply {
                duration = 2400
                startDelay = 200 * 6
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }

        val bezierAnimator8LeftSide =
            ObjectAnimator.ofFloat(likeButton8LeftSide, View.X, View.Y, leftSidePath).apply {
                duration = 2400
                startDelay = 200 * 7
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val fadeAnimator8LeftSide =
            ObjectAnimator.ofFloat(likeButton8LeftSide, "alpha", 1f, 0f).apply {
                duration = 2400
                startDelay = 200 * 7
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val rotateAnimator8LeftSide =
            ObjectAnimator.ofFloat(likeButton8LeftSide, "rotation", 360f, 0f).apply {
                duration = 2400
                startDelay = 200 * 7
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }

        val bezierAnimator9LeftSide =
            ObjectAnimator.ofFloat(likeButton9LeftSide, View.X, View.Y, leftSidePath).apply {
                duration = 2400
                startDelay = 200 * 8
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val fadeAnimator9LeftSide =
            ObjectAnimator.ofFloat(likeButton9LeftSide, "alpha", 1f, 0f).apply {
                duration = 2400
                startDelay = 200 * 8
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val rotateAnimator9LeftSide =
            ObjectAnimator.ofFloat(likeButton9LeftSide, "rotation", 360f, 0f).apply {
                duration = 2400
                startDelay = 200 * 8
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val bezierAnimator10LeftSide =
            ObjectAnimator.ofFloat(likeButton10LeftSide, View.X, View.Y, leftSidePath).apply {
                duration = 2400
                startDelay = 200 * 9
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val fadeAnimator10LeftSide =
            ObjectAnimator.ofFloat(likeButton10LeftSide, "alpha", 1f, 0f).apply {
                duration = 2400
                startDelay = 200 * 9
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val rotateAnimator10LeftSide =
            ObjectAnimator.ofFloat(likeButton10LeftSide, "rotation", 360f, 0f).apply {
                duration = 2400
                startDelay = 200 * 9
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }

        val bezierAnimator11LeftSide =
            ObjectAnimator.ofFloat(likeButton11LeftSide, View.X, View.Y, leftSidePath).apply {
                duration = 2400
                startDelay = 200 * 10
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val fadeAnimator11LeftSide =
            ObjectAnimator.ofFloat(likeButton11LeftSide, "alpha", 1f, 0f).apply {
                duration = 2400
                startDelay = 200 * 10
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val rotateAnimator11LeftSide =
            ObjectAnimator.ofFloat(likeButton11LeftSide, "rotation", 360f, 0f).apply {
                duration = 2400
                startDelay = 200 * 10
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val bezierAnimator12LeftSide =
            ObjectAnimator.ofFloat(likeButton12LeftSide, View.X, View.Y, leftSidePath).apply {
                duration = 2400
                startDelay = 200 * 11
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val fadeAnimator12LeftSide =
            ObjectAnimator.ofFloat(likeButton12LeftSide, "alpha", 1f, 0f).apply {
                duration = 2400
                startDelay = 200 * 11
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        val rotateAnimator12LeftSide =
            ObjectAnimator.ofFloat(likeButton12LeftSide, "rotation", 360f, 0f).apply {
                duration = 2400
                startDelay = 200 * 11
                repeatCount = ObjectAnimator.INFINITE
                repeatMode = ObjectAnimator.RESTART
            }
        return listOf(
            bezierAnimator1,
            bezierAnimator2,
            bezierAnimator3,
            bezierAnimator4,
            bezierAnimator5,
            bezierAnimator6,
            bezierAnimator7,
            bezierAnimator8,
            bezierAnimator9,
            bezierAnimator10,
            bezierAnimator11,
            bezierAnimator12,
            fadeAnimator1,
            fadeAnimator2,
            fadeAnimator3,
            fadeAnimator4,
            fadeAnimator5,
            fadeAnimator6,
            fadeAnimator7,
            fadeAnimator8,
            fadeAnimator9,
            fadeAnimator10,
            fadeAnimator11,
            fadeAnimator12,
            bezierAnimator1LeftSide,
            bezierAnimator2LeftSide,
            bezierAnimator3LeftSide,
            bezierAnimator4LeftSide,
            bezierAnimator5LeftSide,
            bezierAnimator6LeftSide,
            bezierAnimator7LeftSide,
            bezierAnimator8LeftSide,
            bezierAnimator9LeftSide,
            bezierAnimator10LeftSide,
            bezierAnimator11LeftSide,
            bezierAnimator12LeftSide,
            fadeAnimator1LeftSide,
            fadeAnimator2LeftSide,
            fadeAnimator3LeftSide,
            fadeAnimator4LeftSide,
            fadeAnimator5LeftSide,
            fadeAnimator7LeftSide,
            fadeAnimator6LeftSide,
            fadeAnimator8LeftSide,
            fadeAnimator9LeftSide,
            fadeAnimator10LeftSide,
            fadeAnimator11LeftSide,
            fadeAnimator12LeftSide,
            rotateAnimator1,
            rotateAnimator2,
            rotateAnimator3,
            rotateAnimator4,
            rotateAnimator5,
            rotateAnimator6,
            rotateAnimator7,
            rotateAnimator8,
            rotateAnimator9,
            rotateAnimator10,
            rotateAnimator11,
            rotateAnimator12,
            rotateAnimator1LeftSide,
            rotateAnimator2LeftSide,
            rotateAnimator3LeftSide,
            rotateAnimator4LeftSide,
            rotateAnimator5LeftSide,
            rotateAnimator6LeftSide,
            rotateAnimator7LeftSide,
            rotateAnimator8LeftSide,
            rotateAnimator9LeftSide,
            rotateAnimator10LeftSide,
            rotateAnimator11LeftSide,
            rotateAnimator12LeftSide
        )
    }

    override fun onDismiss(dialog: DialogInterface) {
        if (listener != null) {
            listener?.onTrophyDialogDismissed(true)
            listener = null
        }
        super.onDismiss(dialog)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return activity?.let {
            // Use the Builder class for convenient dialog construction
            val builder = AlertDialog.Builder(it)
            customLayout = layoutInflater.inflate(R.layout.trophy_layout, null)
//            customLayout.findViewById<TextView>(R.id.textView2).text =
//                getString(R.string.congratulations_you_won_100_impressions, interactionLimit)
            customLayout.findViewById<Button>(R.id.button_claim).setOnClickListener {
                sendClaimYourRewardEvent()
                dismissAllowingStateLoss()
            }
            val trophy = customLayout.findViewById<AppCompatImageView>(R.id.trophy)
            val likeButton = customLayout.findViewById<AppCompatImageButton>(R.id.like_button1)
            trophy.viewTreeObserver.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    trophy.viewTreeObserver.removeOnGlobalLayoutListener(this)


                    val trophyWidth = trophy.width
                    val likeButtonWidth = likeButton.width
                    val rightSideCoordinateX = trophy.right + trophyWidth
                    val rightSideCoordinateY = trophy.top - trophyWidth
                    val rightSideCoordinateXTwo = trophy.right + trophyWidth / 2
                    val rightSideCoordinateYTwo = trophy.bottom

                    //test variables for ending points for the right side
                    val rightSideEndingPointX = trophy.right - trophyWidth / 4
                    val rightSideEndingPointY = trophy.bottom

                    val rightSidePath = Path().apply {
                        moveTo((trophy.right - trophyWidth / 2).toFloat(), (trophy.top).toFloat())
                        cubicTo(
                            rightSideCoordinateX.toFloat(),
                            rightSideCoordinateY.toFloat(),
                            rightSideCoordinateXTwo.toFloat(),
                            rightSideCoordinateYTwo.toFloat(),
                            rightSideEndingPointX.toFloat(),
                            rightSideEndingPointY.toFloat()
                        )
                    }

                    val leftSideCoordinateX = trophy.left - trophyWidth - likeButtonWidth
                    val leftSideCoordinateY = trophy.top - trophyWidth
                    val leftSideCoordinateXTwo = trophy.left - trophyWidth / 2 - likeButtonWidth
                    val leftSideCoordinateYTwo = trophy.bottom

                    //test variables for ending points for the right side
                    val leftSideEndingPointX = trophy.left + trophyWidth / 4 - likeButtonWidth
                    val leftSideEndingPointY = trophy.bottom

                    val leftSidePath = Path().apply {
                        moveTo(
                            (trophy.left + trophyWidth / 2 - likeButtonWidth).toFloat(),
                            (trophy.top).toFloat()
                        )
                        cubicTo(
                            leftSideCoordinateX.toFloat(),
                            leftSideCoordinateY.toFloat(),
                            leftSideCoordinateXTwo.toFloat(),
                            leftSideCoordinateYTwo.toFloat(),
                            leftSideEndingPointX.toFloat(),
                            leftSideEndingPointY.toFloat()
                        )

                    }


                    val animations = createAnimations(rightSidePath, leftSidePath, customLayout)

                    AnimatorSet().apply {
                        playTogether(animations)
                        start()
                        doOnEnd {
                            start()
                        }
                    }

                }
            })
            builder.setView(customLayout)
            // Create the AlertDialog object and return it
            builder.create().apply {
                this.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

            }
        } ?: throw IllegalStateException("Activity cannot be null")
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        // Verify that the host activity implements the callback interface
        try {
            // Instantiate the TrophyDialogListener so we can send events to the host
            listener = context as TrophyDialogListener
        } catch (e: ClassCastException) {
            // The activity doesn't implement the interface, throw exception
            throw ClassCastException(
                (context.toString() +
                        " must implement ReportCardListener")
            )
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        if (arguments != null) {
            val args = arguments
            interactionLimit = args?.getString(INTERACTION_LIMIT)
        }
        firebaseLogEvent(
            FirebaseAnalyticsEventsName.CLAIM_IMPRESSIONS_BUTTONCLICK, mapOf(
                FirebaseAnalyticsParameterName.CLAIM_IMPRESSIONS_COUNT.value to 1L
            )
        )

        sendYourFriendAcceptedTheInvitationEvent()
    }

    private fun sendYourFriendAcceptedTheInvitationEvent(){
        val eventPremiumType = getPremiumTypeEventProperty(DuaApplication.instance.userRepository.user.value)
        firebaseLogEvent(FirebaseAnalyticsEventsName.YOUR_FRIEND_ACCEPTED_THE_INVITATION)
        sendClevertapEvent(ClevertapEventEnum.YOUR_FRIEND_ACCEPTED_THE_INVITATION, mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType))
    }

    private fun sendClaimYourRewardEvent(){
        val eventPremiumType = getPremiumTypeEventProperty(DuaApplication.instance.userRepository.user.value)

        firebaseLogEvent(FirebaseAnalyticsEventsName.CLAIM_YOUR_REWARD)
        sendClevertapEvent(ClevertapEventEnum.CLAIM_YOUR_REWARD, mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType))
    }
}

