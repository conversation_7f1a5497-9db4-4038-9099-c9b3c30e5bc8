package com.duaag.android.rewards.highlight.boost

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.FOCUS_BLOCK_DESCENDANTS
import android.widget.FrameLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.fragment.app.DialogFragment
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapInstructionTypeValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.BoostHighlightDialogBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.InstructionTypeValues
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.updateLocale


class BoostHighlightDialog : DialogFragment() {

    private var _binding: BoostHighlightDialogBinding? = null
    private val binding get() = _binding

    private var highlightData: BoostHighlightData? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        updateLocale(context)
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.FullScreenTransparentDialogStyle)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = BoostHighlightDialogBinding.inflate(inflater, container, false)
        highlightData = getHighlightData(arguments)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        highlightData?.let { data ->
            binding?.let { binding ->
                applyConstraints(requireContext(),data, binding.itemWrapper, binding.parentConstraintLayout)
                setupDismissOnTouch(binding)
                sendEvents(data.communityEventProperty)
            }
        }
    }

    private fun sendEvents(communityEventProperty: String?) {
        val premiumTypeValue = getPremiumTypeEventProperty(DuaApplication.instance.isLoggedInUserPremium())
        sendClevertapEvent(ClevertapEventEnum.INSTRUCTION_SCREEN,
            mapOf(ClevertapEventPropertyEnum.INSTRUCTION_TYPE.propertyName to ClevertapInstructionTypeValues.BOOST.value,
                ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to ClevertapInstructionTypeValues.REWARD.value,
                ClevertapEventPropertyEnum.COMMUNITY.propertyName to communityEventProperty,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumTypeValue)
        )

        firebaseLogEvent(FirebaseAnalyticsEventsName.INSTRUCTION_SCREEN,
            mapOf(FirebaseAnalyticsParameterName.INSTRUCTION_TYPE.value to InstructionTypeValues.BOOST.value,
                FirebaseAnalyticsParameterName.EVENT_SOURCE.value to InstructionTypeValues.REWARD.value,
                FirebaseAnalyticsParameterName.COMMUNITY.value to communityEventProperty)
        )
    }

    /**
     * Retrieves the BoostHighlightData object from the provided arguments,
     * handling potential deprecation and compatibility with different Android versions.
     *
     * @param arguments The Bundle containing the arguments, potentially holding the data.
     * @return The retrieved BoostHighlightData object, or null if not found or an error occurred.
     */

    private fun getHighlightData(arguments: Bundle?): BoostHighlightData? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arguments?.getParcelable(ARG_HIGHLIGHT_DATA, BoostHighlightData::class.java)
        } else {
            @Suppress("DEPRECATION")
            arguments?.getParcelable(ARG_HIGHLIGHT_DATA) as? BoostHighlightData
        }
    }

    /**
     * Makes any touch on the dialog dismiss it.
     */
    @SuppressLint("ClickableViewAccessibility")
    private fun setupDismissOnTouch(binding: BoostHighlightDialogBinding) {
        binding.parentConstraintLayout.setOnClickListener { dismissAllowingStateLoss() }
        binding.parentConstraintLayout.descendantFocusability = FOCUS_BLOCK_DESCENDANTS
        binding.btnGotIt.setOnSingleClickListener{ dismissAllowingStateLoss() }
    }


    /**
     * Applies constraints to the views to position them correctly on the screen.
     *
     * @param data The [BoostHighlightData] containing the positioning information.
     */
    private fun applyConstraints(
        context: Context,
        data: BoostHighlightData,
        itemWrapper: FrameLayout,
        parentConstraintLayout: ConstraintLayout
    ) {
        val resources = context.resources
        val displayMetrics = resources.displayMetrics
        val density = displayMetrics.density
        val widthPixels = displayMetrics.widthPixels
        val padding =(11*density).toInt() //stroke effect of the highlight
        val endMarginPixels = widthPixels - (data.xOnScreen + data.itemWidth) - padding
        val topMarginPixels = data.yOnScreen

        val constraintSet = ConstraintSet()
        constraintSet.clone(parentConstraintLayout)
        constraintSet.connect(
            itemWrapper.id,
            ConstraintSet.END,
            parentConstraintLayout.id,
            ConstraintSet.END,
            endMarginPixels
        )
        constraintSet.connect(
            itemWrapper.id,
            ConstraintSet.TOP,
            ConstraintSet.PARENT_ID,
            ConstraintSet.TOP,
            topMarginPixels
        )
        constraintSet.applyTo(parentConstraintLayout)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        highlightData = null
        _binding = null
    }

    companion object {
        const val TAG = "BoostHighlightDialog"
        private const val ARG_HIGHLIGHT_DATA = "arg_highlight_data"

        fun newInstance(data: BoostHighlightData? = null): BoostHighlightDialog {
            val fragment = BoostHighlightDialog()
            val args = Bundle()
            args.putParcelable(ARG_HIGHLIGHT_DATA, data)
            fragment.arguments = args
            return fragment
        }
    }
}

