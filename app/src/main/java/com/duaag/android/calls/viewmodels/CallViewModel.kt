package com.duaag.android.calls.viewmodels

import android.content.ComponentName
import android.content.ServiceConnection
import android.os.IBinder
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.duaag.android.api.Resource
import com.duaag.android.api.Result
import com.duaag.android.calls.models.*
import com.duaag.android.calls.repositories.VideoCallsRepository
import com.duaag.android.calls.services.CallService
import com.duaag.android.calls.utils.CameraCapturerCompat
import com.duaag.android.chat.ChatRepository
import com.duaag.android.di.ActivityScope
import com.duaag.android.user.UserRepository
import com.duaag.android.utils.livedata.SingleLiveData
import com.twilio.audioswitch.AudioDevice
import com.twilio.video.LocalVideoTrack
import com.twilio.video.VideoTrack
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch
import okhttp3.ResponseBody
import timber.log.Timber
import javax.inject.Inject

@ActivityScope
class CallViewModel @Inject constructor(
    val userRepository: UserRepository,
    val chatRepository: ChatRepository,
    val videoCallsRepository: VideoCallsRepository
) : ViewModel() {

    companion object {
        private const val TAG = "CallViewModel"
    }

    private var mService: CallService? = null

    private val _binder: MutableLiveData<CallService.CallBinder?> = MutableLiveData()
    val binder: LiveData<CallService.CallBinder?>
        get() = _binder

    private val _callStarted: SingleLiveData<Result<StartCallResponse>> = SingleLiveData()
    val callStarted: LiveData<Result<StartCallResponse>>
        get() = _callStarted

    private val _callAccepted: SingleLiveData<Result<ResponseBody>> = SingleLiveData()
    val callAccepted: LiveData<Result<ResponseBody>>
        get() = _callAccepted

    private val _callEnded: SingleLiveData<Result<String>> = SingleLiveData()
    val callEnded: LiveData<Result<String>>
        get() = _callEnded

    private val _callFailed: SingleLiveData<Result<Long>> = SingleLiveData()
    val callFailed: LiveData<Result<Long>>
        get() = _callFailed

    private var _callDuration: LiveData<Long> = MutableLiveData()
    val callDuration: LiveData<Long>
        get() = _callDuration

    private var _bluetoothDeviceConnected: LiveData<Void> = MutableLiveData()
    val bluetoothDeviceConnected: LiveData<Void>
        get() = _bluetoothDeviceConnected

    var isEndingCall = false

    // Keeping this in here because it doesn't require a context
    private val serviceConnection: ServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(className: ComponentName, iBinder: IBinder) {
            Timber.tag(TAG).d("ServiceConnection: connected to service.")
            // We've bound to MyService, cast the IBinder and get MyBinder instance
            val binder: CallService.CallBinder = iBinder as CallService.CallBinder
            mService = binder.service
            _binder.postValue(binder)

            _callDuration = mService?.callDuration!!
            _bluetoothDeviceConnected = mService?.bluetoothDeviceConnected!!
        }

        override fun onServiceDisconnected(arg0: ComponentName) {
            Timber.tag(TAG).d("ServiceConnection: disconnected from service.")
            mService = null
            _binder.postValue(null)
        }
    }

    fun getServiceConnection(): ServiceConnection {
        return serviceConnection
    }

    fun stopCallService() {
        binder.value?.service?.stopService()
    }

    fun startCall(startCallBody: StartCallBody) {
        viewModelScope.launch(Dispatchers.IO) {
            mService?.setCallState(CallState.CALLING)

            videoCallsRepository.startCall(startCallBody)
                .catch { ex ->
                    when (ex) {
                        is UserVideoCallsLimitReachedException -> {
                            _callFailed.postValue(Result.Error(ex))
                        }
                        is UserVideoCallsNotAllowedException -> {
                            _callFailed.postValue(Result.Error(ex))
                        }
                        is UserBusyException -> {
                            _callFailed.postValue(Result.Error(ex))
                        }
                        else -> {
                            _callFailed.postValue(Result.Error(ex as Exception))
                        }
                    }
                    mService?.setCallState(CallState.CALL_ENDED)
                    ex.printStackTrace()
                }
                .collect {
                    when (it) {
                        is Resource.Success -> {
                            mService?.playRingingSound()
                            mService?.setStartedRingingTime()
                            mService?.updateCallModel(it.data)
                            mService?.connectToRoom(it.data.token, it.data.roomId)
                            _callStarted.postValue(Result.Success(it.data))
                        }
                        else -> {
                            _callStarted.postValue(Result.Loading)
                        }
                    }
                }
        }
    }

    fun acceptCall(callerUserId: String) {
        mService?.setCallState(CallState.CONNECTING)

        viewModelScope.launch(Dispatchers.IO) {
            videoCallsRepository.acceptCall(callerUserId)
                .catch { ex ->
                    ex.printStackTrace()
                    _callEnded.postValue(Result.Success(""))
                }
                .collect {
                    when (it) {
                        is Resource.Success -> {

                            val token = mService?.callModel?.receiverToken!!
                            val roomId = mService?.callModel?.roomId!!
                            val isVideoEnabled = mService?.callModel?.isVoiceCall != true
                            mService?.connectToRoom(token, roomId)

                            _callAccepted.postValue(Result.Success(it.data))
                        }
                        else -> {
                            _callAccepted.postValue(Result.Loading)
                        }
                    }
                }
        }
    }

    fun endCall(endCallBody: EndCallBody? = null) {
        isEndingCall = true

        val reason = if (mService?.isCaller == true) {
            if (mService?.didParticipantJoin == true)
                VideoCallEndedReason.DEFAULT
            else
                VideoCallEndedReason.CALLER_CANCELLED
        } else {
            VideoCallEndedReason.DEFAULT
        }

        val model = endCallBody ?: EndCallBody(
                mService?.isCaller ?: false,
                mService?.callModel?.conversationId!!,
                reason.type
        )

        //log call duration when ending
        val duration = if (mService?.callDuration?.value != null) mService?.callDuration?.value!! else 0
        Timber.tag("ANALYTICSLOG").d("callDuration: $duration")
        if (duration > 0) {
            mService?.logCallDuration(duration)
        }

        viewModelScope.launch(Dispatchers.IO) {
            videoCallsRepository.endCall(model)
                    .catch { ex ->
                        mService?.setCallState(CallState.CALL_ENDED)
                        _callEnded.postValue(Result.Success(""))
                        isEndingCall = false
                        ex.printStackTrace()
                    }
                    .collect {
                        when (it) {
                            is Resource.Success -> {
                                mService?.setCallState(CallState.CALL_ENDED)
                                _callEnded.postValue(Result.Success(""))
                                isEndingCall = false
                            }
                            else -> {
                                _callEnded.postValue(Result.Loading)
                            }
                        }
                    }
        }
    }

    fun setCallEnded(reason: String?) {
        _callEnded.postValue(Result.Success(reason ?: ""))
    }

    fun switchCamera() {
        mService?.switchCamera()
    }

    fun getCameraSource(): CameraCapturerCompat.Source? {
        return mService?.getCameraSource()
    }

    fun getCallState(): CallState? {
        return mService?.getCallState()
    }

    fun toggleAudioDevice(): AudioDevice? {
        return mService?.toggleAudioDevice()
    }

    fun setAudioDevice(audioDevice: AudioOutputDevice): Boolean {
        return mService?.setAudioDevice(audioDevice.type) ?: false
    }

    fun createLocalVideoTrack(isVideoEnabled: Boolean): LocalVideoTrack? {
        return mService?.createLocalVideoTrack(isVideoEnabled)
    }

    fun getParticipantIdentity(): String? {
        return mService?.getParticipantIdentity()
    }

    fun getRemoteVideoTrack(): VideoTrack? {
        return mService?.getRemoteVideoTrack()
    }

    fun publishLocalVideoTrack() {
        mService?.publishLocalVideoTrack()
    }

    fun unPublishLocalVideoTrack() {
        mService?.unPublishLocalVideoTrack()
    }

    override fun onCleared() {
        super.onCleared()
        mService = null
    }

    fun hasAnyVideoTrack(): Boolean {
        return mService?.hasAnyVideoTrack() ?: false
    }

    fun getSelectedAudioDevice(): AudioDevice? {
        return mService?.getSelectedAudioDevice()
    }

    fun hasBluetoothDevice(): Boolean {
        return mService?.hasBluetoothDevice() ?: false
    }

    fun isBluetoothAudioSelected(): Boolean {
        return mService?.isBluetoothSelected() ?: false
    }

    fun hasParticipantJoined(): Boolean {
        return mService?.hasParticipantJoined() ?: false
    }

    fun isReconnecting(): Boolean {
        return mService?.isReconnecting ?: false
    }

    fun hasSwitchedCallMode(): Boolean {
        return mService?.hasSwitchedCallMode() ?: false
    }

    fun setHasSwitchedCallMode() {
        mService?.setHasSwitchedMode()
    }
}