package com.duaag.android.firebase.model

import android.os.Bundle
import com.duaag.android.ads.likedyou.models.NotificationRewardModel
import com.duaag.android.boost.models.BoostResultModel
import com.duaag.android.chat.model.ConversationData
import com.duaag.android.chat.model.LikeMessageModel
import com.duaag.android.clevertap.CrossPathCreatedNotificationModel
import com.duaag.android.clevertap.PremiumIdModel
import com.duaag.android.clevertap.PurchaselyPaywalllModel
import com.duaag.android.clevertap.offers.RealTimeClevertapOfferModel
import com.duaag.android.firebase.NotificationType
import com.duaag.android.home.models.ImpressionsRewarded
import com.duaag.android.premium_subscription.models.SpecialOfferDataModel
import com.google.firebase.messaging.RemoteMessage
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

class NotificationModel {

    var type: NotificationType = NotificationType.NONE
    var data: Any? = ""
    var mRemoteMessage: RemoteMessage? = null
    var mExtras: Bundle? = null

    constructor(remoteMessage: RemoteMessage) {
        mRemoteMessage = remoteMessage

        type = when (remoteMessage.data["action"]) {
            "chat.newMessage" -> NotificationType.MESSAGE
            "users.newMatch" -> NotificationType.MATCH
            "users.newLikeReceived" -> NotificationType.NEW_LIKE_RECEIVED
            "users.unmatched" -> NotificationType.UN_MATCHED
            "counters.counterReset" -> NotificationType.COUNTER_RESET
            "users.deleted" -> NotificationType.USER_DELETED
            "users.profileCompletionReminder" -> NotificationType.PROFILE_COMPLETION_REMINDER
            "users.verifyUserAttribute" -> NotificationType.VERIFY_USER_ATTRIBUTE
            "users.newReferralReward" -> NotificationType.NEW_REFERRAL_REWARD
            "chat.isLikedMessageStateChanged" -> NotificationType.IS_LIKED_MESSAGE_STATE_CHANGED
            "updateRemoteConfig" ->  NotificationType.UPDATE_REMOTE_CONFIG
            "users.dislikeInstachat" ->  NotificationType.DISLIKE_INSTACHAT
            "users.badge2Updated" ->  NotificationType.BADGE_2
            "videoCalls.newVideoCall" ->  NotificationType.VIDEOCALLS_NEWVIDEOCALL
            "videoCalls.endVideoCall" -> NotificationType.END_VIDEO_CALL
            "videoCalls.videoCallAccepted" -> NotificationType.VIDEO_CALL_ACCEPTED
            "users.verifyImage" -> NotificationType.VERIFY_YOUR_IMAGE
            "in-app-purchases.newLocalSubscription" -> NotificationType.NEW_LOCAL_SUBSCRIPTION
            "download-data.userDataRequestProcessed" -> NotificationType.DOWNLOAD_DATA
            "users.impressionsRewarded" -> NotificationType.IMPRESSIONS_REWARDED
            "users.disabledStateChanged" -> NotificationType.DISABLED_STATE_CHANGED
            "openPremiumPaywall" -> NotificationType.OPEN_PREMIUM_PAYWALL
            "premiumSpecialOffer" -> NotificationType.PREMIUM_SPECIAL_OFFER
            "premiumPay1Get1Offer" -> NotificationType.PREMIUM_PAY_1_GET_1
            "users.boostSuccess" -> NotificationType.BOOST_SUCCESS
            "users.boostFailed" -> NotificationType.BOOST_FAILED
            "users.push_badge2_verification" -> NotificationType.PUSH_BADGE2_VERIFICATION
            "users.instachatLastHour" -> NotificationType.INSTACHAT_LASTHOUR
            "users.RMODLastHour" -> NotificationType.RMOD_LASTHOUR
            "in-app-purchases.showDontLetGoOffer" -> NotificationType.SHOW_DONT_LET_GO_OFFER
            "users.newGroupedLikes" -> NotificationType.NEW_GROUPED_LIKES
            "premiumOffer" -> NotificationType.PREMIUM_OFFER
            "setupAccountCredentials" -> NotificationType.SETUP_ACCOUNT_CREDENTIALS
            "purchaselyPaywall" -> NotificationType.PURCHASELY_PAYWALL
            "forcePurchaselyPaywall" -> NotificationType.FORCE_PURCHASELY_PAYWALL
            "purchaselyBoostOffer" -> NotificationType.PURCHASELY_BOOST_PAYWALL
            "users.rewardMaleUsersWithExtraInteractions" -> NotificationType.EXTRA_SWIPES_REWARDED_MALE
            "users.rewardVideos" -> NotificationType.REWARD_VIDEOS
            "users.featuredUsersReset" -> NotificationType.FEATURED_USERS_RESET
            "users.consumableRewardGiven" -> NotificationType.CONSUMABLE_REWARD_GIVEN
            "users.newProfileVisitReceived" -> NotificationType.NEW_PROFILE_VISIT_RECEIVED
            "showInstachatPaywall" -> NotificationType.SHOW_INSTACHAT_PAYWALL
            "showBoostPaywall" -> NotificationType.SHOW_BOOST_PAYWALL
            "users.premiumRemovedDueToGenderChange" -> NotificationType.PREMIUM_REMOVED_DUE_TO_GENDER_CHANGE
            "recommender.RMODGenerated" -> NotificationType.RMOD_GENERATED
            "recommender.RMODRemoved" -> NotificationType.RMOD_REMOVED
            "chat.conversationDeleted" -> NotificationType.CONVERSATION_DELETED
            "realTimePurchaselyOffer" -> NotificationType.REAL_TIME_PURCHASELY_OFFER
            "users.crossPathCreated" -> NotificationType.CROSS_PATH_CREATED
            "users.pendingLocationPinReminder" -> NotificationType.CROSS_PATH_SYNC



            else -> NotificationType.NONE
        }

        data = when (type) {
            NotificationType.MESSAGE -> Gson().fromJson(remoteMessage.data["jsonData"], ConversationData::class.java)
            NotificationType.MATCH -> Gson().fromJson<ArrayList<UserMatchNotificationResponse>>(remoteMessage.data["jsonData"], object : TypeToken<ArrayList<UserMatchNotificationResponse>>() {}.type)
            NotificationType.NEW_LIKE_RECEIVED -> Gson().fromJson(remoteMessage.data["jsonData"], UserLikedYouNotificationResponse::class.java)
            NotificationType.UN_MATCHED -> Gson().fromJson(remoteMessage.data["jsonData"], UserUnMatchedModel::class.java)
            NotificationType.COUNTER_RESET -> Gson().fromJson(remoteMessage.data["jsonData"], CounterResetDataModel::class.java)
            NotificationType.USER_DELETED ->  Gson().fromJson(remoteMessage.data["jsonData"], UserDeletedModel::class.java)
            NotificationType.PROFILE_COMPLETION_REMINDER ->  "PROFILE_COMPLETION_REMINDER"
            NotificationType.VERIFY_USER_ATTRIBUTE -> "VERIFY_USER_ATTRIBUTE"
            NotificationType.NEW_REFERRAL_REWARD -> "NEW_REFERRAL_REWARD"
            NotificationType.IS_LIKED_MESSAGE_STATE_CHANGED -> Gson().fromJson(remoteMessage.data["jsonData"], LikeMessageModel::class.java)
            NotificationType.UPDATE_REMOTE_CONFIG -> "UPDATE_REMOTE_CONFIG"
            NotificationType.DISLIKE_INSTACHAT -> Gson().fromJson(remoteMessage.data["jsonData"], DislikeInstaChatModel::class.java)
            NotificationType.BADGE_2 -> Gson().fromJson(remoteMessage.data["jsonData"], Badge2ApprovalModel::class.java)
            NotificationType.VIDEOCALLS_NEWVIDEOCALL -> Gson().fromJson(remoteMessage.data["jsonData"], CallModel::class.java)
            NotificationType.END_VIDEO_CALL -> Gson().fromJson(remoteMessage.data["jsonData"], EndVideoCallModel::class.java)
            NotificationType.VIDEO_CALL_ACCEPTED -> Gson().fromJson(remoteMessage.data["jsonData"], VideoCallAcceptedModel::class.java)
            NotificationType.VERIFY_YOUR_IMAGE -> "VERIFY_YOUR_IMAGE"
            NotificationType.DOWNLOAD_DATA -> "DOWNLOAD_DATA"
            NotificationType.NEW_LOCAL_SUBSCRIPTION -> Gson().fromJson(remoteMessage.data["jsonData"], NewLocalSubscriptionModel::class.java)
            NotificationType.IMPRESSIONS_REWARDED -> Gson().fromJson(remoteMessage.data["jsonData"], ImpressionsRewarded::class.java)
            NotificationType.DISABLED_STATE_CHANGED -> Gson().fromJson(remoteMessage.data["jsonData"], DisableStateChanged::class.java)
            NotificationType.OPEN_PREMIUM_PAYWALL -> "openPremiumPaywall"
            NotificationType.PREMIUM_SPECIAL_OFFER -> Gson().fromJson(remoteMessage.data["jsonData"], SpecialOfferDataModel::class.java)
            NotificationType.PREMIUM_PAY_1_GET_1 -> Gson().fromJson(remoteMessage.data["jsonData"], SpecialOfferDataModel::class.java)
            NotificationType.BOOST_SUCCESS -> Gson().fromJson(remoteMessage.data["jsonData"], BoostResultModel::class.java)
            NotificationType.BOOST_FAILED -> Gson().fromJson(remoteMessage.data["jsonData"], BoostResultModel::class.java)
            NotificationType.PUSH_BADGE2_VERIFICATION -> Gson().fromJson(remoteMessage.data["jsonData"], PushBadge2VerificationModel::class.java)
            NotificationType.INSTACHAT_LASTHOUR ->  Gson().fromJson(remoteMessage.data["jsonData"], LastHourInstachatModel::class.java)
            NotificationType.RMOD_LASTHOUR ->  "users.RMODLastHour"
            NotificationType.SHOW_DONT_LET_GO_OFFER -> Gson().fromJson(remoteMessage.data["jsonData"],DontLetGoOfferNotificationModel::class.java)
            NotificationType.NEW_GROUPED_LIKES -> "users.newGroupedLikes"
            NotificationType.PREMIUM_OFFER -> Gson().fromJson(remoteMessage.data["jsonData"],PremiumIdModel::class.java)
            NotificationType.SETUP_ACCOUNT_CREDENTIALS -> "setupAccountCredentials"
            NotificationType.PURCHASELY_PAYWALL -> Gson().fromJson(remoteMessage.data["jsonData"], PurchaselyPaywalllModel::class.java)
            NotificationType.FORCE_PURCHASELY_PAYWALL -> Gson().fromJson(remoteMessage.data["jsonData"], PurchaselyPaywalllModel::class.java)
            NotificationType.PURCHASELY_BOOST_PAYWALL -> Gson().fromJson(remoteMessage.data["jsonData"], PurchaselyPaywalllModel::class.java)
            NotificationType.EXTRA_SWIPES_REWARDED_MALE -> "users.rewardMaleUsersWithExtraInteractions"
            NotificationType.REWARD_VIDEOS -> Gson().fromJson(remoteMessage.data["jsonData"], NotificationRewardModel::class.java)
            NotificationType.FEATURED_USERS_RESET -> "users.featuredUsersReset"
            NotificationType.CONSUMABLE_REWARD_GIVEN -> Gson().fromJson(remoteMessage.data["jsonData"], ConsumableRewardGivenModel::class.java)
            NotificationType.NEW_PROFILE_VISIT_RECEIVED -> "users.newProfileVisitReceived"
            NotificationType.SHOW_INSTACHAT_PAYWALL -> "showInstachatPaywall"
            NotificationType.SHOW_BOOST_PAYWALL -> "showBoostPaywall"
            NotificationType.PREMIUM_REMOVED_DUE_TO_GENDER_CHANGE -> "users.premiumRemovedDueToGenderChange"
            NotificationType.RMOD_GENERATED -> "recommender.RMODGenerated"
            NotificationType.RMOD_REMOVED -> "recommender.RMODRemoved"
            NotificationType.CONVERSATION_DELETED -> Gson().fromJson(remoteMessage.data["jsonData"], ConversationDeletedModel::class.java)
            NotificationType.REAL_TIME_PURCHASELY_OFFER -> Gson().fromJson(remoteMessage.data["jsonData"], RealTimeClevertapOfferModel::class.java)
            NotificationType.CROSS_PATH_CREATED -> Gson().fromJson(remoteMessage.data["jsonData"],CrossPathCreatedNotificationModel::class.java)
            NotificationType.CROSS_PATH_SYNC -> "users.pendingLocationPinReminder"
            NotificationType.NONE -> "NONE"
        }
    }

    constructor(extras: Bundle) {
        mExtras = extras

        type = when (extras.getString("action")) {
            "chat.newMessage" -> NotificationType.MESSAGE
            "users.newMatch" -> NotificationType.MATCH
            "users.newLikeReceived" -> NotificationType.NEW_LIKE_RECEIVED
            "users.unmatched" -> NotificationType.UN_MATCHED
            "counters.counterReset" -> NotificationType.COUNTER_RESET
            "users.deleted" -> NotificationType.USER_DELETED
            "users.profileCompletionReminder" -> NotificationType.PROFILE_COMPLETION_REMINDER
            "users.verifyUserAttribute" -> NotificationType.VERIFY_USER_ATTRIBUTE
            "users.newReferralReward" -> NotificationType.NEW_REFERRAL_REWARD
            "chat.isLikedMessageStateChanged" -> NotificationType.IS_LIKED_MESSAGE_STATE_CHANGED
            "updateRemoteConfig" ->  NotificationType.UPDATE_REMOTE_CONFIG
            "users.dislikeInstachat" ->  NotificationType.DISLIKE_INSTACHAT
            "users.badge2Updated" ->  NotificationType.BADGE_2
            "videoCalls.newVideoCall" ->  NotificationType.VIDEOCALLS_NEWVIDEOCALL
            "videoCalls.endVideoCall" -> NotificationType.END_VIDEO_CALL
            "videoCalls.videoCallAccepted" -> NotificationType.VIDEO_CALL_ACCEPTED
            "users.verifyImage" -> NotificationType.VERIFY_YOUR_IMAGE
            "download-data.userDataRequestProcessed" -> NotificationType.DOWNLOAD_DATA
            "in-app-purchases.newLocalSubscription" -> NotificationType.NEW_LOCAL_SUBSCRIPTION
            "users.impressionsRewarded" -> NotificationType.IMPRESSIONS_REWARDED
            "users.disabledStateChanged" -> NotificationType.DISABLED_STATE_CHANGED
            "openPremiumPaywall" -> NotificationType.OPEN_PREMIUM_PAYWALL
            "premiumSpecialOffer" -> NotificationType.PREMIUM_SPECIAL_OFFER
            "premiumPay1Get1Offer" -> NotificationType.PREMIUM_PAY_1_GET_1
            "users.boostSuccess" -> NotificationType.BOOST_SUCCESS
            "users.boostFailed" -> NotificationType.BOOST_FAILED
            "users.push_badge2_verification" -> NotificationType.PUSH_BADGE2_VERIFICATION
            "users.instachatLastHour" -> NotificationType.INSTACHAT_LASTHOUR
            "users.RMODLastHour" -> NotificationType.RMOD_LASTHOUR
            "in-app-purchases.showDontLetGoOffer" -> NotificationType.SHOW_DONT_LET_GO_OFFER
            "users.newGroupedLikes" -> NotificationType.NEW_GROUPED_LIKES
            "premiumOffer" -> NotificationType.PREMIUM_OFFER
            "setupAccountCredentials" -> NotificationType.SETUP_ACCOUNT_CREDENTIALS
            "purchaselyPaywall" -> NotificationType.PURCHASELY_PAYWALL
            "forcePurchaselyPaywall" -> NotificationType.FORCE_PURCHASELY_PAYWALL
            "purchaselyBoostOffer" -> NotificationType.PURCHASELY_BOOST_PAYWALL
            "users.rewardMaleUsersWithExtraInteractions" -> NotificationType.EXTRA_SWIPES_REWARDED_MALE
            "users.rewardVideos" -> NotificationType.REWARD_VIDEOS
            "users.featuredUsersReset" -> NotificationType.FEATURED_USERS_RESET
            "users.consumableRewardGiven" -> NotificationType.CONSUMABLE_REWARD_GIVEN
            "users.newProfileVisitReceived" -> NotificationType.NEW_PROFILE_VISIT_RECEIVED
            "showInstachatPaywall" -> NotificationType.SHOW_INSTACHAT_PAYWALL
            "showBoostPaywall" -> NotificationType.SHOW_BOOST_PAYWALL
            "users.premiumRemovedDueToGenderChange" -> NotificationType.PREMIUM_REMOVED_DUE_TO_GENDER_CHANGE
            "recommender.RMODGenerated" -> NotificationType.RMOD_GENERATED
            "recommender.RMODRemoved" -> NotificationType.RMOD_REMOVED
            "chat.conversationDeleted" -> NotificationType.CONVERSATION_DELETED
            "realTimePurchaselyOffer" -> NotificationType.REAL_TIME_PURCHASELY_OFFER
            "users.crossPathCreated" -> NotificationType.CROSS_PATH_CREATED
            "users.pendingLocationPinReminder" -> NotificationType.CROSS_PATH_SYNC

            else -> NotificationType.NONE
        }

        data = when (type) {
            NotificationType.MESSAGE -> Gson().fromJson(extras.getString("jsonData"), ConversationData::class.java)
            NotificationType.MATCH -> Gson().fromJson<ArrayList<UserMatchNotificationResponse>>(extras.getString("jsonData")
                    , object : TypeToken<ArrayList<UserMatchNotificationResponse>>() {}.type)
            NotificationType.NEW_LIKE_RECEIVED -> Gson().fromJson(extras.getString("jsonData")
                    , UserLikedYouNotificationResponse::class.java)
            NotificationType.UN_MATCHED -> Gson().fromJson(extras.getString("jsonData"), UserUnMatchedModel::class.java)
            NotificationType.COUNTER_RESET -> Gson().fromJson(extras.getString("jsonData"), CounterResetDataModel::class.java)
            NotificationType.USER_DELETED -> Gson().fromJson(extras.getString("jsonData"), UserDeletedModel::class.java)
            NotificationType.PROFILE_COMPLETION_REMINDER ->  "PROFILE_COMPLETION_REMINDER"
            NotificationType.VERIFY_USER_ATTRIBUTE -> "VERIFY_USER_ATTRIBUTE"
            NotificationType.NEW_REFERRAL_REWARD -> "NEW_REFERRAL_REWARD"
            NotificationType.IS_LIKED_MESSAGE_STATE_CHANGED -> Gson().fromJson(extras.getString("jsonData"), LikeMessageModel::class.java)
            NotificationType.UPDATE_REMOTE_CONFIG -> "UPDATE_REMOTE_CONFIG"
            NotificationType.DISLIKE_INSTACHAT -> Gson().fromJson(extras.getString("jsonData"), DislikeInstaChatModel::class.java)
            NotificationType.BADGE_2 -> Gson().fromJson(extras.getString("jsonData"), Badge2ApprovalModel::class.java)
            NotificationType.VIDEOCALLS_NEWVIDEOCALL -> Gson().fromJson(extras.getString("jsonData"), CallModel::class.java)
            NotificationType.END_VIDEO_CALL -> Gson().fromJson(extras.getString("jsonData"), EndVideoCallModel::class.java)
            NotificationType.VIDEO_CALL_ACCEPTED -> Gson().fromJson(extras.getString("jsonData"), VideoCallAcceptedModel::class.java)
            NotificationType.VERIFY_YOUR_IMAGE -> "VERIFY_YOUR_IMAGE"
            NotificationType.DOWNLOAD_DATA -> "DOWNLOAD_DATA"
            NotificationType.NEW_LOCAL_SUBSCRIPTION -> Gson().fromJson(extras.getString("jsonData"), NewLocalSubscriptionModel::class.java)
            NotificationType.IMPRESSIONS_REWARDED -> Gson().fromJson(extras.getString("jsonData"), ImpressionsRewarded::class.java)
            NotificationType.DISABLED_STATE_CHANGED -> Gson().fromJson(extras.getString("jsonData"), DisableStateChanged::class.java)
            NotificationType.OPEN_PREMIUM_PAYWALL -> "openPremiumPaywall"
            NotificationType.PREMIUM_SPECIAL_OFFER -> Gson().fromJson(extras.getString("jsonData"), SpecialOfferDataModel::class.java)
            NotificationType.PREMIUM_PAY_1_GET_1 -> Gson().fromJson(extras.getString("jsonData"), SpecialOfferDataModel::class.java)
            NotificationType.BOOST_SUCCESS -> Gson().fromJson(extras.getString("jsonData"), BoostResultModel::class.java)
            NotificationType.BOOST_FAILED -> Gson().fromJson(extras.getString("jsonData"), BoostResultModel::class.java)
            NotificationType.PUSH_BADGE2_VERIFICATION -> Gson().fromJson(extras.getString("jsonData"), PushBadge2VerificationModel::class.java)
            NotificationType.INSTACHAT_LASTHOUR -> Gson().fromJson(extras.getString("jsonData"), LastHourInstachatModel::class.java)
            NotificationType.RMOD_LASTHOUR -> "users.RMODLastHour"
            NotificationType.SHOW_DONT_LET_GO_OFFER -> Gson().fromJson(extras.getString("jsonData"),DontLetGoOfferNotificationModel::class.java)
            NotificationType.NEW_GROUPED_LIKES -> "users.newGroupedLikes"
            NotificationType.PREMIUM_OFFER -> Gson().fromJson(extras.getString("jsonData"),PremiumIdModel::class.java)
            NotificationType.SETUP_ACCOUNT_CREDENTIALS -> "setupAccountCredentials"
            NotificationType.PURCHASELY_PAYWALL -> Gson().fromJson(extras.getString("jsonData"), PurchaselyPaywalllModel::class.java)
            NotificationType.FORCE_PURCHASELY_PAYWALL -> Gson().fromJson(extras.getString("jsonData"), PurchaselyPaywalllModel::class.java)
            NotificationType.PURCHASELY_BOOST_PAYWALL -> Gson().fromJson(extras.getString("jsonData"), PurchaselyPaywalllModel::class.java)
            NotificationType.EXTRA_SWIPES_REWARDED_MALE -> "users.rewardMaleUsersWithExtraInteractions"
            NotificationType.REWARD_VIDEOS -> Gson().fromJson(extras.getString("jsonData"), NotificationRewardModel::class.java)
            NotificationType.FEATURED_USERS_RESET -> "users.featuredUsersReset"
            NotificationType.CONSUMABLE_REWARD_GIVEN -> Gson().fromJson(extras.getString("jsonData"), ConsumableRewardGivenModel::class.java)
            NotificationType.NEW_PROFILE_VISIT_RECEIVED -> "users.newProfileVisitReceived"
            NotificationType.SHOW_INSTACHAT_PAYWALL -> "showInstachatPaywall"
            NotificationType.SHOW_BOOST_PAYWALL -> "showBoostPaywall"
            NotificationType.PREMIUM_REMOVED_DUE_TO_GENDER_CHANGE -> "users.premiumRemovedDueToGenderChange"
            NotificationType.RMOD_GENERATED -> "recommender.RMODGenerated"
            NotificationType.RMOD_REMOVED -> "recommender.RMODRemoved"
            NotificationType.CONVERSATION_DELETED -> Gson().fromJson(extras.getString("jsonData"), ConversationDeletedModel::class.java)
            NotificationType.REAL_TIME_PURCHASELY_OFFER -> Gson().fromJson(extras.getString("jsonData"), RealTimeClevertapOfferModel::class.java)
            NotificationType.CROSS_PATH_CREATED -> Gson().fromJson(extras.getString("jsonData"),CrossPathCreatedNotificationModel::class.java)
            NotificationType.CROSS_PATH_SYNC -> "users.pendingLocationPinReminder"
            NotificationType.NONE -> "NONE"

        }
    }
}