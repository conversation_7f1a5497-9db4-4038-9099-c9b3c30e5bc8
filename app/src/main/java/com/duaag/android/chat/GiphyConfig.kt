package com.duaag.android.chat

import com.giphy.sdk.core.models.enums.RenditionType
import com.giphy.sdk.ui.GPHContentType
import com.giphy.sdk.ui.themes.GPHTheme
import com.giphy.sdk.ui.themes.GridType

object GiphyConfig {

    var contentType = GPHContentType.gif
    val gridType = GridType.waterfall
    val theme = GPHTheme.Light
    val renditionType= RenditionType.fixedWidth
    const val showSuggestions=false

}