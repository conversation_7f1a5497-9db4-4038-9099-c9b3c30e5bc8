package com.duaag.android.chat.blockspamlinks.utils

import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone

fun Long.convertHourToMilliSeconds(hours :Long): Long {
    return hours * 60 * 60 * 1000
}

// Extension function to convert ISO 8601 timestamp to milliseconds
fun String.toMillis(): Long? {
    // Define the date format for ISO 8601
    val dateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())
    dateFormat.timeZone = TimeZone.getTimeZone("UTC")

    // Parse the date
    val date: Date? = dateFormat.parse(this)

    // Convert the date to milliseconds
    return date?.time
}