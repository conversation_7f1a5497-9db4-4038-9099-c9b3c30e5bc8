package com.duaag.android.chat.model

import com.google.gson.annotations.SerializedName

class MessagesResponse(@SerializedName("messages")
                       val messages: List<MessageModel>,
                       @SerializedName("nextCursor")
                       val nextCursor: String? = null,
                       @SerializedName("previousCursor")
                       val previousCursor: String? = null)