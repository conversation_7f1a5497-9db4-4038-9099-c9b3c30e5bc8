package com.duaag.android.chat.blockspamlinks.data.db.models

import androidx.annotation.Keep
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

@Keep
@Entity(tableName = "spam_link")
data class SpamLinkModel(
    @SerializedName("id")
    @PrimaryKey(autoGenerate = true)
    val id: Int = 0,
    @SerializedName("url")
    val url: String,
    @SerializedName("time")
    val time: Long
)
