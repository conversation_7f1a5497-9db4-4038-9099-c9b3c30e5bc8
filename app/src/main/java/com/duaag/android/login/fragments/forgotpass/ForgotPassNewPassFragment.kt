package com.duaag.android.login.fragments.forgotpass

import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.amazonaws.services.cognitoidentityprovider.model.LimitExceededException
import com.amazonaws.services.cognitoidentityprovider.model.UserLambdaValidationException
import com.amazonaws.services.cognitoidentityprovider.model.UserNotFoundException
import com.duaag.android.R
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.clevertap.*
import com.duaag.android.databinding.FragmentForgotpassNewPasswordBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.logSignUpEvent
import com.duaag.android.login.StartActivity
import com.duaag.android.login.viewmodels.SignInViewModel
import com.duaag.android.signup.models.ForgotPasswordAuthResult
import com.duaag.android.utils.*
import com.duaag.android.utils.isPasswordValid
import javax.inject.Inject

class ForgotPassNewPassFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val signInViewModel by viewModels<SignInViewModel>({ activity as StartActivity }) { viewModelFactory }
    private var _binding: FragmentForgotpassNewPasswordBinding? = null
    private val binding get() = _binding!!

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as StartActivity).loginComponent.inject(this)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        _binding = FragmentForgotpassNewPasswordBinding.inflate(inflater, container, false)

        sendLogInEventCleverTapUxCam(ClevertapEventEnum.NEW_PASSWORD_SCREENVIEW, signInViewModel.authMethod.value)

        checkIfPasswordInputHasChanged()

        binding.btnContinue.setOnClickListener {
            logSignUpEvent(signInViewModel.authMethod.value, FirebaseAnalyticsEventsName.FP_NEW_PASSWORD)

            validateInput(signInViewModel.userPassword)
        }

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        signInViewModel.forgotPassResultLiveData.observe(viewLifecycleOwner, Observer {
            when (it) {
                is ForgotPasswordAuthResult.Success -> {
                    val action = ForgotPassNewPassFragmentDirections.actionForgotPassNewPassFragmentV2ToForgotPass6DigitCodeFragmentV2()
                    findNavController().navigate(action)
                }
                is ForgotPasswordAuthResult.Error -> {
                    when (it.e) {
                        is UserNotFoundException -> {
                            val message = getString(R.string.wrong_param)
                            ToastUtil.toast(message)
                        }

                        is UserLambdaValidationException -> {
                            when {
                                it.e.message?.contains("token_not_accepted") == true -> {
                                    val message = getString(R.string.an_error_occurred)
                                    ToastUtil.toast(message)
                                }

                                it.e.message?.contains("CustomMessage") == true -> {
                                    val message = getHourFromErrorMessage(it.e.errorMessage)
                                    ToastUtil.toast(message)
                                }

                                else -> {
                                    val message = getString(R.string.an_error_occurred)
                                    ToastUtil.toast(message)
                                }
                            }

                        }

                        is LimitExceededException -> {
                            val message = getHourFromErrorMessage(it.e.errorMessage)
                            ToastUtil.toast(message)
                        }

                        else -> {
                            val message = getString(R.string.an_error_occurred)
                            ToastUtil.toast(message)
                        }
                    }
                    logError(ErrorStatus.FORGOT_PASS_NEW_PASS_RESULT_LIVE_DATA)
                }
                is ForgotPasswordAuthResult.Loading -> {
                    binding.btnContinue.isEnabled = false
                    binding.btnContinue.isClickable = false
                }

                is ForgotPasswordAuthResult.CustomSmsVerification ->{
                    signInViewModel.onCustomSmsVerificationUsed(true)
                    val action = ForgotPassNewPassFragmentDirections.actionForgotPassNewPassFragmentV2ToForgotPass6DigitCodeFragmentV2()
                    findNavController().navigate(action)
                }
            }
        })

    }

    private fun checkIfPasswordInputHasChanged() {
        binding.let {
            it.passwordInput.addTextChangedListener(object : TextWatcher {
                var previousText = ""
                override fun afterTextChanged(s: Editable?) {
                    val newText = s.toString()
                    if(previousText == newText ) return
                    signInViewModel.setPassword(s.toString())
                    if (binding.passwordInput.text.isNotEmpty()) {
                        shouldContinueButton(true)
                    } else {
                        shouldContinueButton(false)
                    }                }

                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
                    previousText = s.toString()
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    clearPasswordErrorMessage()
                }
            })
            it.passwordInput.onKeyboardNext {

            }
        }

    }

    private fun setPasswordError() {
        binding.passwordErrorText.visibility = View.VISIBLE
        binding.passwordErrorText.setTextColorRes(R.color.red_500)
        binding.passwordInputLayout.background = ContextCompat.getDrawable(requireContext(), R.drawable.error_corners_12dp)
        binding.passwordInputLayout.requestFocus()
    }


    private fun clearPasswordErrorMessage() {
        binding.passwordErrorText.visibility = View.GONE
        binding.passwordInputLayout.background = ContextCompat.getDrawable(requireContext(), R.drawable.edit_text_rounded_corners_12_dp)
    }


    private fun validateInput(password: String) {
        if (password.isNotEmpty() && isPasswordValid(password)) {
            (requireActivity() as StartActivity).executeForgotPassReCaptcha()
            signInViewModel.setTimer()
        } else
            setPasswordError()
    }


    private fun shouldContinueButton(isEnabled: Boolean) {
        binding.btnContinue.isEnabled = isEnabled
        binding.btnContinue.isClickable = isEnabled
    }
}





