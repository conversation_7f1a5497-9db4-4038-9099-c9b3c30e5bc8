package com.duaag.android.login.fragments.forgotpass

import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.View
import androidx.viewpager2.widget.ViewPager2
import com.duaag.android.R
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator

class CustomTabLayoutMediator(
    private val tabLayout: TabLayout,
    private val viewPager: ViewPager2,
    private val tabConfigurationStrategy: TabLayoutMediator.TabConfigurationStrategy
) {
    private var mediator: TabLayoutMediator? = null
    private var leftIndicator: Drawable? = null
    private var rightIndicator: Drawable? = null

    fun attach() {
        // Get the drawables
        val context = tabLayout.context
        leftIndicator = context.getDrawable(R.drawable.tab_indicator_left)
        rightIndicator = context.getDrawable(R.drawable.tab_indicator_right)

        // Create and attach the standard mediator
        mediator = TabLayoutMediator(tabLayout, viewPager, tabConfigurationStrategy)
        mediator?.attach()

        // Set up the initial indicator
        updateTabIndicators(viewPager.currentItem)

        // Add a tab selection listener to update the indicators
        tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                updateTabIndicators(tab.position)
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {
                // Not needed
            }

            override fun onTabReselected(tab: TabLayout.Tab) {
                // Not needed
            }
        })

        // Add a page change callback to update the indicators
        viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                updateTabIndicators(position)
            }
        })
    }

    private fun updateTabIndicators(selectedPosition: Int) {
        // Get the tab count
        val tabCount = tabLayout.tabCount
        if (tabCount <= 0) return

        // First, clear all custom views to ensure proper state
        for (i in 0 until tabCount) {
            val tab = tabLayout.getTabAt(i) ?: continue
            tab.customView = null
        }

        // Now set the custom view only for the selected tab
        val selectedTab = tabLayout.getTabAt(selectedPosition) ?: return

        // Set the appropriate indicator based on position
        val customViewResId = when (selectedPosition) {
            0 -> R.layout.custom_tab_left
            tabCount - 1 -> R.layout.custom_tab_right
            else -> R.layout.custom_tab_middle
        }

        // Inflate the custom view
        val inflater = LayoutInflater.from(tabLayout.context)
        val customView = inflater.inflate(customViewResId, null)

        // Set the text
        val textView = customView.findViewById<android.widget.TextView>(R.id.tab_text)
        textView.text = selectedTab.text

        // Set the custom view to the tab
        selectedTab.customView = customView
    }

    fun detach() {
        mediator?.detach()
        mediator = null
    }
}
