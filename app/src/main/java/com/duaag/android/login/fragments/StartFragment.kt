package com.duaag.android.login.fragments

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding
import androidx.credentials.CredentialManager
import androidx.credentials.CustomCredential
import androidx.credentials.GetCredentialRequest
import androidx.credentials.GetCredentialResponse
import androidx.credentials.GetCustomCredentialOption
import androidx.credentials.exceptions.GetCredentialException
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import com.duaag.android.BuildConfig
import com.duaag.android.R
import com.duaag.android.api.ConnectivityErrorResponse
import com.duaag.android.api.ResourceV2
import com.duaag.android.api.Result
import com.duaag.android.aws.models.LoginModel
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapOnboardingTypeValues
import com.duaag.android.clevertap.ClevertapSignUpOrSignInMediumValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.clevertap.sendClevertapEventWithoutCommunity
import com.duaag.android.databinding.FragmentStartFragmentBinding
import com.duaag.android.exceptions.AccounDevicetIsBlacklistedException
import com.duaag.android.exceptions.AccountIsBlacklistedException
import com.duaag.android.exceptions.DevicetIsBlacklistedException
import com.duaag.android.exceptions.InvalidGoogleTokenException
import com.duaag.android.exceptions.UserAlreadyExistException
import com.duaag.android.last_login.data.remote.dto.LastLoggedInErrorResponse
import com.duaag.android.last_login.domain.model.AccountType
import com.duaag.android.last_login.domain.model.LastLoggedIn
import com.duaag.android.launcher.SplashActivity.Companion.SIGN_UP_WITH_SPOTTED_INTENT_FILTER
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.login.ChooseAuthProviderBottomSheet
import com.duaag.android.login.StartActivity
import com.duaag.android.login.StartActivity.Companion.SPOTTED_TOKEN
import com.duaag.android.login.dialogs.ContinueToSignInDialog
import com.duaag.android.login.models.AuthModel
import com.duaag.android.login.models.SignUpWithSpottedBody
import com.duaag.android.login.models.SignUpWithSpottedRawMapper
import com.duaag.android.login.models.ThirdPartyUserData
import com.duaag.android.login.viewmodels.SignInViewModel
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.signup.SignUpActivity
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.utils.AgeUtils
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.NetworkChecker
import com.duaag.android.utils.NonceGenerator
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.createBlacklistDialog
import com.duaag.android.utils.createCalendarFromThirdPartyBirthday
import com.duaag.android.utils.facebookPermissions
import com.duaag.android.utils.getStringResourceByName
import com.duaag.android.utils.hideKeyboard
import com.duaag.android.utils.isBirthdayAllowed
import com.duaag.android.utils.makeLinks
import com.duaag.android.utils.openWebView
import com.duaag.android.utils.setMargin
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.updateLocale
import com.duaag.android.uxcam.sendUxCamEvent
import com.facebook.CallbackManager
import com.facebook.FacebookCallback
import com.facebook.FacebookException
import com.facebook.GraphRequest
import com.facebook.login.LoginManager
import com.facebook.login.LoginResult
import com.google.android.libraries.identity.googleid.GetSignInWithGoogleOption
import com.google.android.libraries.identity.googleid.GoogleIdTokenCredential
import com.google.android.libraries.identity.googleid.GoogleIdTokenParsingException
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONObject
import timber.log.Timber
import java.util.Calendar
import java.util.UUID
import javax.inject.Inject


class StartFragment : Fragment(),
    ContinueToSignInDialog.ContinueToSignInListener,
    ChooseAuthProviderBottomSheet.OnAuthProviderSelectedListener {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val signInViewModel by viewModels<SignInViewModel>({ activity as StartActivity }) { viewModelFactory }

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs

    private var credentialManager: CredentialManager? = null

    private var _binding: FragmentStartFragmentBinding? = null
    private val binding get() = _binding!!

    private lateinit var navController: NavController
    private lateinit var callbackManager: CallbackManager
    private lateinit var links: Map<String?, String>


    private val spottedTokenBroadcastReceiver by lazy { SpottedTokenBroadcastReceiver() }


    private val connectivityReceiver = ConnectivityReceiver()

    companion object {
        const val TAG = "Login"
        const val LOG_IN_REQUEST_CODE = 148
    }

    inner class ConnectivityReceiver: BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val isNetworkConnected = NetworkChecker.isNetworkConnected(context!!)
            if (isNetworkConnected) {
                if(signInViewModel.lastLoginInfo.value is ResourceV2.Error) {
                    showLoadingIndicator()
                    signInViewModel.getLastLoginInfoAPI(signInViewModel.visitorId)
                } else if(signInViewModel.lastLoginInfo.value is ResourceV2.Success) {
                    handleLastLoginResult(signInViewModel.lastLoginInfo.value!!)
                }
            } else {
                showNoInternetConnectionScreen()
            }
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as StartActivity).loginComponent.inject(this)
        Timber.tag("VIEWMODEL").d(signInViewModel.toString())
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        ContextCompat.registerReceiver(
            requireContext(),
            spottedTokenBroadcastReceiver,
            IntentFilter(SIGN_UP_WITH_SPOTTED_INTENT_FILTER),
            ContextCompat.RECEIVER_NOT_EXPORTED
        )
        callbackManager = CallbackManager.Factory.create()
        setUpWebLinks()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupFacebookAuth()
        checkSignUpWithSpotted()
        navController = findNavController()
        setOnClickListeners()
        credentialManager = CredentialManager.create(requireActivity())


        signInViewModel.lastLoginInfo.observe(viewLifecycleOwner) { result ->
            if (result == null)
                return@observe

            handleLastLoginResult(result)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = FragmentStartFragmentBinding.inflate(inflater)

        if(BuildConfig.DEBUG) {
            binding.resetBtn.visibility = View.VISIBLE
            binding.resetBtn.setOnSingleClickListener {
                showNewUserUI()
            }
        }

        binding.agreement.text = getString(R.string.onboarding_info_full_an)
        activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)

        signInViewModel.setSignInCurrentPage(null)
        // Adjust the view to stretch under the navigation bar
        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { v, insets ->
            val navigationBarInsets = insets.getInsets(WindowInsetsCompat.Type.navigationBars())

            // Adjust the padding to stretch under the navigation bar
            v.updatePadding(bottom = navigationBarInsets.bottom)
            insets
        }

        ContextCompat.registerReceiver(
            requireContext(),
            connectivityReceiver,
            IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION),
            ContextCompat.RECEIVER_EXPORTED
        )

        return binding.root
    }


    private fun handleLastLoginResult(result: ResourceV2<LastLoggedIn>) {
        when (result) {
            is ResourceV2.Success -> {

                binding.rootContainer.visibility = View.VISIBLE
                binding.loadingAccount.visibility = View.GONE

                when (result.data.accountType) {
                    AccountType.GOOGLE,
                    AccountType.FACEBOOK,
                    AccountType.EMAIL,
                    AccountType.PHONE -> {
                        showExistingUserUI(result.data)
                    }
                    AccountType.UNKNOWN -> {
                        showNewUserUI()
                    }
                }
                sendScreenViewEvent(result.data.accountType)
            }
            is ResourceV2.Error -> {
                if(result.errorType == ConnectivityErrorResponse.NO_INTERNET_CONNECTION) {
                    showNoInternetConnectionScreen()
                } else if(result.errorType == LastLoggedInErrorResponse.AN_ERROR_OCCURRED) {
                    showNewUserUI()
                }
            }
        }
    }

    override fun onStart() {
        super.onStart()
        hideKeyboard()
    }


    override fun onAuthProviderSelected(provider: ChooseAuthProviderBottomSheet.AuthProvider, isSignUp: Boolean) {
        when (provider) {
            ChooseAuthProviderBottomSheet.AuthProvider.GOOGLE -> {
                lifecycleScope.launch {
                    if(isSignUp)
                        sendSignInInitiatedEvent(AuthMethod.GOOGLE.methodName)
                    else
                        sendOnboardingContinueWithClickedEvent(AuthMethod.GOOGLE.methodName, ClevertapOnboardingTypeValues.SIGN_UP)

                    delay(300)

                    onContinueWithGoogle()
                }
            }
            ChooseAuthProviderBottomSheet.AuthProvider.FACEBOOK -> {
                lifecycleScope.launch {
                    if(isSignUp)
                        sendSignInInitiatedEvent(AuthMethod.FACEBOOK.methodName)
                    else
                        sendOnboardingContinueWithClickedEvent(AuthMethod.FACEBOOK.methodName, ClevertapOnboardingTypeValues.SIGN_UP)

                    delay(300)

                    binding.facebookLoginBtn.performClick()
                }
            }
            ChooseAuthProviderBottomSheet.AuthProvider.EMAIL_PHONE -> {
                lifecycleScope.launch {
                    if(isSignUp)
                        sendSignInInitiatedEvent(null)
                    else
                        sendOnboardingContinueWithClickedEvent(null, ClevertapOnboardingTypeValues.SIGN_UP)

                    delay(300)

                    when(isSignUp) {
                        true -> onCreateAccountClicked()
                        false -> onLoginClicked()
                    }
                }

            }
        }
    }

    private fun sendContinueToSignInEvent() {
        sendClevertapEvent(ClevertapEventEnum.CONTINUE_TO_SIGN_IN)
        sendUxCamEvent(ClevertapEventEnum.CONTINUE_TO_SIGN_IN)
    }

    private fun sendContinueToSignUpEvent() {
        sendClevertapEvent(ClevertapEventEnum.CONTINUE_TO_SIGN_UP)
        sendUxCamEvent(ClevertapEventEnum.CONTINUE_TO_SIGN_UP)
    }

    private fun sendSignInInitiatedEvent(provider: String?) {
        sendClevertapEvent(ClevertapEventEnum.SIGN_IN_INITIATED,
            mapOf(ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to provider)
        )
        sendUxCamEvent(ClevertapEventEnum.SIGN_IN_INITIATED,
            mapOf(ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to provider)
        )
    }

    private fun sendOnboardingContinueWithClickedEvent(provider: String?, onboardingType: ClevertapOnboardingTypeValues) {
        sendClevertapEvent(
            ClevertapEventEnum.ONBOARDING_CONTINUE_WITH_CLICKED,
            mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to provider,
                ClevertapEventPropertyEnum.ONBOARDING_TYPE.propertyName to onboardingType.value,
            )
        )
        sendUxCamEvent(
            ClevertapEventEnum.ONBOARDING_CONTINUE_WITH_CLICKED,
            mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to provider,
                ClevertapEventPropertyEnum.ONBOARDING_TYPE.propertyName to onboardingType.value
            )
        )
    }

    private fun sendNoInternetConnectionEvent() {
        sendClevertapEvent(ClevertapEventEnum.NO_INTERNET_CONNECTION)
        sendUxCamEvent(ClevertapEventEnum.NO_INTERNET_CONNECTION)
    }

    private fun setUpWebLinks() {
        links = mapOf(
            getString(R.string.onboarding_privacy_link) to requireContext().getStringResourceByName(
                BuildConfig.PRIVACY_POLICY_LINK_KEY
            ), getString(R.string.onboarding_terms_link) to requireContext().getStringResourceByName(
                BuildConfig.TERMS_AND_CONDITIONS_LINK_KEY
            )
        )
    }

    private fun showExistingUserUI(lastLoggedIn: LastLoggedIn) {
        binding.rootContainer.visibility = View.VISIBLE
        binding.existingAccountContainer.visibility = View.VISIBLE
        binding.noAccountContainer.visibility = View.GONE
        binding.noInternetContainer.visibility = View.GONE

        binding.illustration.rotationY = 180f
        when (lastLoggedIn.accountType) {
            AccountType.GOOGLE -> {
                binding.authProviderBtn.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_google_icon, 0, 0, 0)
                binding.authProviderBtn.setText(R.string.continue_with_google)
            }
            AccountType.FACEBOOK -> {
                binding.authProviderBtn.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_facebook_icon, 0, 0, 0)
                binding.authProviderBtn.setText(R.string.continue_with_facebook)
            }
            AccountType.EMAIL -> {
                binding.authProviderBtn.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0)
                binding.authProviderBtn.setText(R.string.onboarding_continuewith_label_email)
            }
            AccountType.PHONE -> {
                binding.authProviderBtn.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0)
                binding.authProviderBtn.setText(R.string.onboarding_continuewith_label_telefon)
            }
            AccountType.UNKNOWN -> {}
        }

        //set the text if the account is deactivated
        if(lastLoggedIn.isDeactivated) {
            binding.deactivatedDescription.visibility = View.VISIBLE
            binding.authProviderBtn.setMargin(bottom = 32f)
        } else {
            binding.deactivatedDescription.visibility = View.GONE
            binding.authProviderBtn.setMargin(bottom = 64f)
        }

        binding.authProviderBtn.setOnSingleClickListener {
            if (signInViewModel.loggingInProgress) return@setOnSingleClickListener

            signInViewModel.lastLoginInfo.value?.data?.accountType?.let {
                when(it) {
                    AccountType.GOOGLE -> {
                        sendOnboardingContinueWithClickedEvent(AuthMethod.GOOGLE.methodName, ClevertapOnboardingTypeValues.LAST_LOG_IN)

                        onContinueWithGoogle()
                        signInViewModel.loggingInProgress = true
                    }
                    AccountType.FACEBOOK -> {
                        sendOnboardingContinueWithClickedEvent(AuthMethod.FACEBOOK.methodName, ClevertapOnboardingTypeValues.LAST_LOG_IN)

                        binding.facebookLoginBtn.performClick()
                        signInViewModel.loggingInProgress = true
                    }
                    AccountType.EMAIL -> {
                        sendOnboardingContinueWithClickedEvent(null, ClevertapOnboardingTypeValues.LAST_LOG_IN)

                        onLoginClicked(AuthMethod.EMAIL)
                    }
                    AccountType.PHONE -> {
                        sendOnboardingContinueWithClickedEvent(null, ClevertapOnboardingTypeValues.LAST_LOG_IN)

                        onLoginClicked(AuthMethod.PHONE)
                    }
                    AccountType.UNKNOWN -> {}
                }
            }
        }
    }

    private fun showNewUserUI() {
        binding.rootContainer.visibility = View.VISIBLE
        binding.noAccountContainer.visibility = View.VISIBLE
        binding.existingAccountContainer.visibility = View.GONE
        binding.noInternetContainer.visibility = View.GONE
        binding.loadingAccount.visibility = View.GONE
        binding.illustration.rotationY = 0f
    }

    private fun showNoInternetConnectionScreen() {
        binding.noInternetContainer.visibility = View.VISIBLE
        binding.rootContainer.visibility = View.GONE
        binding.loadingAccount.visibility = View.GONE

        sendNoInternetConnectionEvent()
    }

    private fun showLoadingIndicator() {
        binding.noInternetContainer.visibility = View.GONE
        binding.rootContainer.visibility = View.GONE
        binding.loadingAccount.visibility = View.VISIBLE
    }

    private fun buildCredentialRequest(googleIdOption: GetCustomCredentialOption): GetCredentialRequest {
        return GetCredentialRequest.Builder()
            .addCredentialOption(googleIdOption)
            .build()
    }

    private suspend fun performCredentialRetrieval(request: GetCredentialRequest) {
        try {
            val result = credentialManager?.getCredential(
                request = request,
                context = requireActivity(),
            )
            result?.let { handleSignIn(it) }
        } catch (e: GetCredentialException) {
            handleCredentialException(e)
        } finally {
            signInViewModel.loggingInProgress = false
        }
    }

    private fun handleCredentialException(e: GetCredentialException) {
        Timber.tag(TAG).d(e, "GetCredentialException: ${e.message}")
        signInViewModel.loggingInProgress = false
    }

    private fun sendScreenViewEvent(accountType: AccountType) {
        val retainData =
            if (accountType == AccountType.UNKNOWN) "new_user" else accountType.name.lowercase()

        sendClevertapEvent(
            ClevertapEventEnum.SIGN_UP_OR_SIGN_IN_SCREENVIEW,
            mapOf(ClevertapEventPropertyEnum.RETAIN_DATA.propertyName to retainData)
        )
        sendUxCamEvent(
            ClevertapEventEnum.SIGN_UP_OR_SIGN_IN_SCREENVIEW,
            mapOf(ClevertapEventPropertyEnum.RETAIN_DATA.propertyName to retainData)
        )
    }

    private fun loginWithCredentials(googleIdOption: GetCustomCredentialOption) {
        if (!isAdded || view == null) return

        signInViewModel.loggingInProgress = true
        val request = buildCredentialRequest(googleIdOption)
        viewLifecycleOwner.lifecycleScope.launch {
            try {
                performCredentialRetrieval(request)
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "Error during credential retrieval")
                signInViewModel.loggingInProgress = false
            }
        }
    }


    private fun onLoginClicked(authMethod: AuthMethod? = null) {
        firebaseLogEvent(FirebaseAnalyticsEventsName.START_SIGN_IN)

        sendClevertapEventWithoutCommunity(ClevertapEventEnum.SIGN_IN_INITIATED, mapOf(
            ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to null
        ))
        sendUxCamEvent(ClevertapEventEnum.SIGN_IN_INITIATED, mapOf(
            ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to null
        ))

        openLoginPhoneScreen(authMethod)
    }

    private fun onCreateAccountClicked() {
        if(signInViewModel.loggingInProgress)
            return

        firebaseLogEvent(FirebaseAnalyticsEventsName.START_SIGN_UP)

        val signUpOrSignInmMedium = ClevertapSignUpOrSignInMediumValues.NULL.value

        sendClevertapEvent(
            ClevertapEventEnum.SIGN_UP_INITIATED, mapOf(ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMedium))

        val signUpIntent = Intent(context, SignUpActivity::class.java)
        signUpIntent.putExtra(SignUpActivity.AUTH_METHOD_EXTRA, AuthMethod.PHONE)
        startActivityForResult(signUpIntent, LOG_IN_REQUEST_CODE)
    }

    private fun setOnClickListeners() {
        binding.createAccountBtn.setOnSingleClickListener {
            if (signInViewModel.loggingInProgress) return@setOnSingleClickListener

            sendContinueToSignUpEvent()

            val bottomSheet = ChooseAuthProviderBottomSheet.newInstance(true)
            bottomSheet.show(childFragmentManager, bottomSheet.tag)
        }

        binding.loginBtn.setOnSingleClickListener {
            if (signInViewModel.loggingInProgress) return@setOnSingleClickListener

            sendContinueToSignInEvent()

            val bottomSheet = ChooseAuthProviderBottomSheet.newInstance(false)
            bottomSheet.show(childFragmentManager, bottomSheet.tag)
        }

        binding.tryAgainBtn.setOnSingleClickListener {
            showLoadingIndicator()
            signInViewModel.getLastLoginInfoAPI(signInViewModel.visitorId)
        }

        binding.agreement.apply {
            val firstPairTitle = getString(R.string.onboarding_terms_link)
            val secondPairTitle = getString(R.string.onboarding_privacy_link)

            val termsLink = links[firstPairTitle].orEmpty()
            val privacyLink = links[secondPairTitle].orEmpty()

            val link1 = Pair(
                first = firstPairTitle,
                second = View.OnClickListener {
                    context?.openWebView(termsLink, getString(R.string.terms_and_conditions), requireActivity())
                }
            )
            val link2 = Pair(
                first = secondPairTitle,
                second = View.OnClickListener {
                    context?.openWebView(privacyLink, getString(R.string.privacy_policy), requireActivity())
                }
            )
            makeLinks(R.color.foundation_description_primary, link1, link2)
        }
    }

    private fun onContinueWithGoogle() {

        val nonce = NonceGenerator.generateNonce()
        val signInWithGoogleOption: GetSignInWithGoogleOption = buildSignInWithGoogleOption(nonce)

        loginWithCredentials(signInWithGoogleOption)

    }

    private fun buildSignInWithGoogleOption(nonce: String): GetSignInWithGoogleOption {
        return GetSignInWithGoogleOption
            .Builder(BuildConfig.GOOGLE_WEB_CLIENT_ID)
            .setNonce(nonce)
            .build()
    }

    private fun handleSignIn(result: GetCredentialResponse) {
        when (val credential = result.credential) {
            is CustomCredential -> {
                if (credential.type == GoogleIdTokenCredential.TYPE_GOOGLE_ID_TOKEN_CREDENTIAL) {
                    try {

                        val googleIdTokenCredential = GoogleIdTokenCredential
                            .createFrom(credential.data)

                        val idToken: String = googleIdTokenCredential.idToken
                        val email: String = googleIdTokenCredential.id
                        val name: String? = googleIdTokenCredential.givenName
                        val profilePicture: String? = googleIdTokenCredential.profilePictureUri?.toString()
                        val userModel = ThirdPartyUserData(
                            AuthMethod.GOOGLE,
                            email, // Use email as the unique ID
                            name,
                            email,
                            null,
                            null,
                            profilePicture
                        )

                        signInViewModel.thirdPartyUserData = userModel
                        loginGoogleAccount(userModel, idToken)

                    } catch (e: GoogleIdTokenParsingException) {
                        Timber.tag(TAG).e(e, "Received an invalid google id token response")
                        signInViewModel.loggingInProgress  = false

                    }
                }
                else {
                    Timber.tag(TAG).e("Unexpected type of credential")
                    signInViewModel.loggingInProgress  = false

                }
            }

            else -> {
                Timber.tag(TAG).e("Unexpected type of credential")
                signInViewModel.loggingInProgress  = false

            }
        }
    }

    private fun loginGoogleAccount(userModel: ThirdPartyUserData, token: String) {
        val randomPassword = UUID.randomUUID().toString()
        val authModel = AuthModel(token, randomPassword, signInViewModel.visitorId)
        signInViewModel.onConnectWithGoogleClicked(authModel).observe(viewLifecycleOwner) {
            when (it) {
                is Result.Success -> {
                    val loginModel = userModel.email?.let { it1 ->
                        LoginModel(
                            it1, randomPassword, AuthMethod.GOOGLE
                        )
                    }
                    if (loginModel != null) {
                        signInViewModel.loginUser(loginModel)
                    } else {
                        signInViewModel.loggingInProgress = false
                    }
                    Timber.tag("GOOGLE_AUTH").d("UserId: ${userModel.id}")

                }

                is Result.Error -> {
                    it.exception.printStackTrace()
                    binding.progressBar.visibility = View.GONE
                    signInViewModel.loggingInProgress = false
                    Timber.tag("GOOGLE_AUTH").d("Error: ${it.exception.message}")

                    when (it.exception) {
                        is AccountIsBlacklistedException -> {
                            val string =
                                "Account ID: ${userModel.id} \n\n\nReason (provide reason):\n"
                            createBlacklistDialog(requireActivity(), string)

                            }
                            is AccounDevicetIsBlacklistedException -> {
                                val string = "Account ID: ${userModel.id}\nDevice ID: ${signInViewModel.visitorId}\n\n\nReason (provide reason):\n"
                                createBlacklistDialog(requireActivity(), string)
                            }
                            is DevicetIsBlacklistedException -> {
                                val string = "Device ID: ${signInViewModel.visitorId}\n\n\nReason (provide reason):\n"
                                createBlacklistDialog(requireActivity(), string)
                            }
                            is InvalidGoogleTokenException -> {
                                ToastUtil.toast(R.string.an_error_occurred)
                            }
                        }
                        logError(ErrorStatus.LOGIN_GOOGLE_ACCOUNT)
                    }
                    is Result.Loading -> {
                        binding.progressBar.visibility = View.VISIBLE
                        Timber.tag("GOOGLE_AUTH").d("Loading")
                    }
                }
            }
    }

    private fun setupFacebookAuth() {
        binding.facebookLoginBtn.setPermissions(*facebookPermissions.toTypedArray())
        binding.facebookLoginBtn.setFragment(this)

        // Callback registration
        binding.facebookLoginBtn.registerCallback(
            callbackManager,
            object : FacebookCallback<LoginResult> {
                override fun onSuccess(loginResult: LoginResult) {
                    Timber.tag(TAG).d("facebook token: ${loginResult.accessToken.token}")
                    signInViewModel.loggingInProgress = true
                    val request: GraphRequest =
                        GraphRequest.newMeRequest(loginResult.accessToken) { jsonObject, response ->
                            Timber.tag("LoginActivity").d(response.toString())
                            if (jsonObject == null) {
                                onCancel()
                                ToastUtil.toast(getString(R.string.smthg_went_wrong))
                                logError(ErrorStatus.FACEBOOK_LOGIN)
                                return@newMeRequest
                            }
                            val userModel = extractFacebookUserData(jsonObject)
                            signInViewModel.thirdPartyUserData = userModel

                    userModel.birthday?.let {
                        if (!AgeUtils.isAgeAllowed(it)) {
                            onCancel()
                            ToastUtil.toast(R.string.facebook_age_not_allowed, Toast.LENGTH_LONG)
                            return@newMeRequest
                        }
                    }
                    val loginFacebookModel = AuthModel(loginResult.accessToken.token, UUID.randomUUID().toString(),
                        signInViewModel.visitorId)

                    if (view != null && isAdded) {
                        signInViewModel.onConnectWithFacebookClicked(loginFacebookModel).observe(viewLifecycleOwner, Observer {
                            when (it) {
                                is Result.Success -> {
                                    val loginModel = LoginModel(loginResult.accessToken.userId, loginFacebookModel.password, AuthMethod.FACEBOOK)
                                    signInViewModel.loginUser(loginModel)
                                    Timber.tag(StartActivity.FACEBOOK_TAG).d("UserId: ${loginResult.accessToken.userId}")
                                }
                                is Result.Error -> {
                                    it.exception.printStackTrace()
                                    binding.progressBar.visibility = View.GONE
                                    signInViewModel.loggingInProgress = false
                                    Timber.tag(StartActivity.FACEBOOK_TAG).d("Error: ${it.exception.message}")
                                    LoginManager.getInstance().logOut()

                                    when (it.exception) {
                                        is AccountIsBlacklistedException -> {
                                            val string = "Account ID: ${userModel.id} \n\n\nReason (provide reason):\n"
                                            createBlacklistDialog(requireActivity(), string)

                                        }
                                        is AccounDevicetIsBlacklistedException -> {
                                            val string = "Account ID: ${userModel.id}\nDevice ID: ${signInViewModel.visitorId}\n\n\nReason (provide reason):\n"
                                            createBlacklistDialog(requireActivity(), string)
                                        }
                                        is DevicetIsBlacklistedException -> {
                                            val string = "Device ID: ${signInViewModel.visitorId}\n\n\nReason (provide reason):\n"
                                            createBlacklistDialog(requireActivity(), string)
                                        }
                                    }
                                }
                                is Result.Loading -> {
                                    binding.progressBar.visibility = View.VISIBLE
                                    Timber.tag(StartActivity.FACEBOOK_TAG).d("Loading")
                                }
                            }
                        })
                    } else {
                        onCancel()
                        ToastUtil.toast(getString(R.string.smthg_went_wrong))
                        logError(ErrorStatus.FACEBOOK_LOGIN)
                        return@newMeRequest
                    }
                }
                val parameters = Bundle()
                parameters.putString("fields", "id,name,email,gender,birthday")
                request.parameters = parameters
                request.executeAsync()
            }

            override fun onCancel() {
                LoginManager.getInstance().logOut()
                signInViewModel.loggingInProgress = false
                Timber.tag(StartActivity.FACEBOOK_TAG).d("cancel")
            }

            override fun onError(e: FacebookException) {
                signInViewModel.loggingInProgress = false
                e.printStackTrace()
                ToastUtil.toast(e.message!!)
            }
        })

        signInViewModel.showHidenFaceBookProgressBar.observe(viewLifecycleOwner, Observer {
            binding.progressBar.visibility = if (it) View.VISIBLE else View.GONE
        })

    }

    fun checkSignUpWithSpotted(spottedTokenBroadcast: String? = null) {
        val spottedToken = spottedTokenBroadcast ?: duaSharedPrefs.getSignUpWithSpottedToken()
        if (spottedToken.isNullOrBlank().not() && signInViewModel.signUpWithSpottedInProgress.not()) {
            signInViewModel.signUpWithSpottedInProgress = true
            val signUpWithSpottedBody = SignUpWithSpottedBody(
                token = spottedToken ?: "",
                password = UUID.randomUUID().toString(),
                deviceId = signInViewModel.visitorId,
                os = "android"
            )
            signInViewModel.signUpWithSpotted(signUpWithSpottedBody).observe(viewLifecycleOwner){ result ->
                when(result) {
                    is Result.Success ->  {
                        val signUpWithSpottedEntity = SignUpWithSpottedRawMapper.map(result.data)
                        signInViewModel.signUpWithSpottedEntity = signUpWithSpottedEntity
                        val loginModel = LoginModel(signUpWithSpottedEntity.username, signUpWithSpottedBody.password)
                        signInViewModel.loginUser(loginModel)
                    }
                    is Result.Error -> {
                        result.exception.printStackTrace()
                        binding.progressBar.visibility = View.GONE
                        when (result.exception) {
                            is AccountIsBlacklistedException -> {
                                val string = "Account ID: ${result.exception.userName} \n\n\nReason (provide reason):\n"
                                createBlacklistDialog(requireActivity(), string)

                            }
                            is AccounDevicetIsBlacklistedException -> {

                                val string = "Account ID: ${result.exception.userName} \nDevice ID: ${signInViewModel.visitorId}\n\n\nReason (provide reason):\n"
                                createBlacklistDialog(requireActivity(), string)
                            }
                            is UserAlreadyExistException -> {
                                ContinueToSignInDialog
                                    .newInstance( result.exception.phone,result.exception.email,
                                        getPremiumTypeEventProperty(signInViewModel.userRepository.user.value)
                                    )
                                    .show(childFragmentManager, "ContinueToSignInDialog")
                            }
                        }
                    }
                    Result.Loading -> binding.progressBar.visibility = View.VISIBLE

                }
            }
        }
    }

    override fun onContinueToSignInPhone(value: String?) {
        signInViewModel.selectedCountryCodeAsInt = value?.substring(0, 4)?.replace("+", "")?.toInt()
        signInViewModel.setPhoneNumber(value ?: "")
        openLoginPhoneScreen(AuthMethod.PHONE)
    }

    override fun onContinueToSignInEmail(value: String?) {
        signInViewModel.setUserEmail(value ?: "")
        openLoginEmailScreen()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        callbackManager.onActivityResult(requestCode, resultCode, data)
        super.onActivityResult(requestCode, resultCode, data)

        if (resultCode == Activity.RESULT_OK) {
            when(requestCode) {
                LOG_IN_REQUEST_CODE -> {
                    openLoginPhoneScreen()
                }
                else -> {}
            }
        }
    }

    fun extractFacebookUserData(jsonObject: JSONObject): ThirdPartyUserData {
        val id: String = jsonObject.getString("id")
        val fullName: String?
        val names: List<String>?
        val birthday: String?
        var email: String? = null
        val fbGender: String?
        var duaGender: GenderType? = null
        var firstName: String? = null
        var calendarBirthday: Calendar? = null

        if (jsonObject.has("name")) {
            fullName = jsonObject.getString("name")
            names = fullName.split(" ")
            firstName = names.firstOrNull()
        }

        if (jsonObject.has("birthday")) {
            birthday = jsonObject.getString("birthday")
            val birthdayModel = createCalendarFromThirdPartyBirthday(birthday)
            birthdayModel?.let {
                if(isBirthdayAllowed(it))
                    calendarBirthday = it
            }

        }

        if (jsonObject.has("email")) {
            email = jsonObject.getString("email")
        }

        if (jsonObject.has("gender")) {
            fbGender = jsonObject.getString("gender")
            duaGender = when (fbGender) {
                "male" -> GenderType.MAN
                "female" -> GenderType.WOMAN
                else -> null
            }
        }

        val picture = "https://graph.facebook.com/${id}/picture?width=10000"

        return ThirdPartyUserData(AuthMethod.FACEBOOK, id, firstName, email, duaGender, calendarBirthday, picture)
    }

    private fun openLoginPhoneScreen(authMethod: AuthMethod? = null){
        val bundle: Bundle = bundleOf()

        if(authMethod != null)
            bundle.putSerializable(SignInFragment.AUTH_METHOD, authMethod)

        navController.navigate(R.id.action_start2Fragment_to_signInFragment, bundle)
    }

    private fun openLoginEmailScreen(){
        val action = StartFragmentDirections.actionStart2FragmentToSignInFragment()
        navController.navigate(action)
    }

    inner class SpottedTokenBroadcastReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val token = intent?.getStringExtra(SPOTTED_TOKEN)
            if (token.isNullOrBlank().not()) {
                checkSignUpWithSpotted(token)
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()

        try {
            binding.facebookLoginBtn.unregisterCallback(callbackManager)
            requireActivity().unregisterReceiver(connectivityReceiver)
        } catch (e: IllegalArgumentException) {
            e.printStackTrace()
        }
        credentialManager = null

        _binding = null
    }

    override fun onDestroy() {
        try {
            requireContext().unregisterReceiver(spottedTokenBroadcastReceiver)
        } catch (e: IllegalArgumentException) {
            e.printStackTrace()
        }
        super.onDestroy()
    }

}