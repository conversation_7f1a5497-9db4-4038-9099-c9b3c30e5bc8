package com.duaag.android.login.fragments

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.viewpager2.widget.ViewPager2
import com.duaag.android.R
import com.duaag.android.databinding.FragmentSignInBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.logSignUpEvent
import com.duaag.android.login.StartActivity
import com.duaag.android.login.fragments.forgotpass.CustomTabLayoutMediator
import com.duaag.android.login.viewmodels.SignInViewModel
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.user_feed.adapters.UserFeedAdapter
import com.duaag.android.utils.hideKeyboard
import com.duaag.android.utils.updateLocale
import javax.inject.Inject

class SignInFragment : Fragment() {

    companion object {
        fun newInstance(): SignInFragment = SignInFragment()
        const val CURRENT_PAGE = "current_page"
        const val SIGN_IN_PHONE_PAGE = 0
        const val SIGN_IN_EMAIL_PAGE = 1

        const val AUTH_METHOD = "auth_method"
    }

    private var _binding: FragmentSignInBinding? = null
    private val binding get() = _binding!!

    private var customTabLayoutMediator: CustomTabLayoutMediator? = null


    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val signInViewModel by viewModels<SignInViewModel> ({ activity as StartActivity }) { viewModelFactory }


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as StartActivity).loginComponent.inject(this)
    }


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = FragmentSignInBinding.inflate(inflater)

        initialize()

        activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)

        signInViewModel.onForgotPasswordClicked.observe(viewLifecycleOwner) { authMethod ->
            logSignUpEvent(authMethod, FirebaseAnalyticsEventsName.FORGOT_PASSWORD)

            findNavController().navigate(R.id.action_signInFragment_to_forgotPasswordFragment,
                bundleOf(CURRENT_PAGE to binding.viewpager.currentItem))

            this.hideKeyboard()
        }

        return binding.root
    }


    fun initialize() {
        val viwPagerAdapter = UserFeedAdapter(childFragmentManager, viewLifecycleOwner.lifecycle)
        viwPagerAdapter.setFragments(listOf(SignInPhoneFragment.newInstance(), SignInEmailFragment.newInstance()))
        binding.viewpager.isUserInputEnabled = true
        binding.viewpager.offscreenPageLimit = 1
        binding.viewpager.adapter = viwPagerAdapter
        val position: Int
        val authMethod = arguments?.getSerializable(AUTH_METHOD) as? AuthMethod
        if(signInViewModel.signInCurrentPage.value == null) {
            position = when(authMethod) {
                AuthMethod.PHONE -> 0
                AuthMethod.EMAIL -> 1
                else -> 0
            }
        } else {
            position = signInViewModel.signInCurrentPage.value ?: 0
        }
        binding.viewpager.setCurrentItem(position, false)

        binding.viewpager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                signInViewModel.setSignInCurrentPage(position)
            }
        })

        setupCustomTabLayout()
    }


    override fun onDestroyView() {
        super.onDestroyView()

        hideKeyboard()

        binding.viewpager.adapter = null

        customTabLayoutMediator?.detach()
        customTabLayoutMediator = null

        _binding = null
    }


    private fun setupCustomTabLayout() {
        binding.tabLayout.setSelectedTabIndicator(null) // Remove the default indicator

        customTabLayoutMediator = CustomTabLayoutMediator(
            binding.tabLayout,
            binding.viewpager
        ) { tab, position ->
            tab.text = when (position) {
                0 -> getString(R.string.phone)
                else -> getString(R.string.email_)
            }
        }

        customTabLayoutMediator?.attach()
    }
}